# 物流分区导入功能 - 前端开发对接文档

## 📋 功能概述

物流分区导入功能允许用户通过 Excel 文件批量导入物流产品的分区配置信息，包括国家分区、地区限制、邮编配置等。

## 🔗 API 接口

### 1. 获取导入模板

**接口地址：** `GET /admin-api/agent/logistics-zone/get-import-template`

**接口描述：** 下载物流分区导入模板 Excel 文件

**请求参数：** 无

**响应：** Excel 文件下载

**前端调用示例：**
```javascript
// 下载导入模板
const downloadTemplate = () => {
  window.open('/admin-api/agent/logistics-zone/get-import-template', '_blank')
}
```

### 2. 导入分区数据

**接口地址：** `POST /admin-api/agent/logistics-zone/import`

**接口描述：** 导入物流分区 Excel 数据

**请求参数：**
- `productId` (Long, 必填): 物流产品ID
- `file` (MultipartFile, 必填): Excel 文件
- `updateSupport` (Boolean, 可选): 是否支持更新已存在数据，默认 false

**响应数据：**
```json
{
  "code": 0,
  "data": {
    "createSuccessCount": 5,      // 创建成功条数
    "updateSuccessCount": 2,      // 更新成功条数  
    "failureCount": 1,            // 失败条数
    "failureData": [              // 失败数据详情
      {
        "row": 3,                 // Excel 行号
        "data": {                 // 原始数据
          "countryCode": "US",
          "zoneCode": "TEST",
          // ... 其他字段
        },
        "reason": "分区编码不能为空"  // 失败原因
      }
    ]
  },
  "msg": ""
}
```

**前端调用示例：**
```javascript
import { upload } from '@/utils/request'

// 导入分区数据
const importZones = async (productId, file, updateSupport = false) => {
  const formData = new FormData()
  formData.append('productId', productId)
  formData.append('file', file)
  formData.append('updateSupport', updateSupport)
  
  return await upload('/admin-api/agent/logistics-zone/import', formData)
}
```

## 📊 Excel 模板格式

### 模板列说明

| 列名 | 字段名 | 类型 | 必填 | 说明 | 示例值 |
|------|--------|------|------|------|--------|
| 国家编码 | countryCode | String | ✅ | ISO 国家代码 | US, AU, FR |
| 分区编码 | zoneCode | String | ✅ | 分区标识码 | FORBIDDEN, REMOTE, ZONE1 |
| 分区名称 | zoneName | String | ✅ | 分区显示名称 | 禁止配送区域, 偏远地区 |
| 一级行政区划 | stateProvince | String | ❌ | 州/省名称 | California, Alaska |
| 二级行政区划 | city | String | ❌ | 城市名称 | Los Angeles, Sydney |
| 三级行政区划 | district | String | ❌ | 区/县名称 | Beverly Hills |
| 特殊区域类型 | specialAreaType | String | ❌ | 特殊区域标识 | ISLAND, MILITARY, TERRITORY |
| 完整区域描述 | fullAreaName | String | ❌ | 完整地区名称 | California Los Angeles |
| 邮编配置JSON | postalCodes | String | ❌ | JSON 格式邮编配置 | ["99500-99999"] |
| 限制类型 | restrictionType | String | ❌ | 配送限制类型 | NORMAL, REMOTE_FEE, FORBIDDEN |
| 附加费公式 | feeFormula | String | ❌ | 费用计算公式 | 8*weight_kg,min:50 |
| 备注说明 | remark | String | ❌ | 备注信息 | 阿拉斯加州禁止配送 |
| 排序 | sort | Integer | ❌ | 排序值 | 1, 2, 3 |
| 状态 | status | Integer | ✅ | 启用状态 | 0-禁用, 1-启用 |

### 字段详细说明

#### 限制类型 (restrictionType)
- `NORMAL`: 正常配送
- `REMOTE_FEE`: 偏远地区，需要额外费用
- `FORBIDDEN`: 禁止配送

#### 特殊区域类型 (specialAreaType)
- `ISLAND`: 岛屿
- `MILITARY`: 军事基地
- `TERRITORY`: 领土/特区
- `OVERSEAS`: 海外领地

#### 邮编配置格式 (postalCodes)
支持多种格式的 JSON 数组：
```json
// 单个邮编
["12345"]

// 邮编范围
["12000-12999"]

// 混合格式
["12345", "13000-13999", "14567"]
```

#### 附加费公式 (feeFormula)
支持多种计费公式：
```
// 固定费用
48

// 重量计费
3*weight_kg

// 重量计费+最低费用
3*weight_kg,min:48

// 重量计费+最高费用
3*weight_kg,max:200

// 阶梯计费
weight_kg<=1:15,weight_kg<=5:25,weight_kg>5:35
```

### 示例数据

```
国家编码 | 分区编码 | 分区名称 | 一级行政区划 | 二级行政区划 | 邮编配置JSON | 限制类型 | 状态
US      | FORBIDDEN| 禁止配送区域| Alaska     |            | ["99500-99999"] | FORBIDDEN | 1
US      | REMOTE   | 偏远地区   | Hawaii     | Honolulu   | ["96700-96899"] | REMOTE_FEE| 1
AU      | ZONE1    | 澳洲1区    | New South Wales| Sydney | ["2000-2999"]   | NORMAL    | 1
```

## 🎨 前端页面实现

### 1. 导入按钮组件

```vue
<template>
  <div class="import-zone-container">
    <!-- 导入按钮 -->
    <el-button
      type="primary"
      icon="Upload"
      @click="showImportDialog = true"
      v-hasPermi="['agent:logistics-zone:import']">
      导入分区
    </el-button>

    <!-- 导入对话框 -->
    <el-dialog
      title="导入物流分区"
      v-model="showImportDialog"
      width="600px">

      <!-- 下载模板 -->
      <div class="mb-4">
        <el-alert
          title="请先下载导入模板，按照模板格式填写数据后再上传"
          type="info"
          show-icon
          :closable="false">
        </el-alert>
        <el-button
          type="text"
          icon="Download"
          @click="downloadTemplate"
          class="mt-2">
          下载导入模板
        </el-button>
      </div>

      <!-- 文件上传 -->
      <el-form :model="importForm" label-width="120px">
        <el-form-item label="物流产品" required>
          <el-select v-model="importForm.productId" placeholder="请选择物流产品">
            <el-option
              v-for="product in productList"
              :key="product.id"
              :label="product.nameZh"
              :value="product.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="Excel文件" required>
          <el-upload
            ref="uploadRef"
            :limit="1"
            accept=".xlsx,.xls"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList">
            <el-button icon="Upload">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="更新支持">
          <el-switch
            v-model="importForm.updateSupport"
            active-text="允许更新已存在数据"
            inactive-text="跳过已存在数据">
          </el-switch>
        </el-form-item>
      </el-form>

      <!-- 对话框按钮 -->
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleImport"
          :loading="importing">
          {{ importing ? '导入中...' : '确定导入' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 导入结果对话框 -->
    <el-dialog
      title="导入结果"
      v-model="showResultDialog"
      width="800px">
      <import-result :result="importResult" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { importLogisticsZones, downloadImportTemplate } from '@/api/agent/logistics-zone'

// 响应式数据
const showImportDialog = ref(false)
const showResultDialog = ref(false)
const importing = ref(false)
const fileList = ref([])
const productList = ref([]) // 从父组件传入或API获取

const importForm = reactive({
  productId: null,
  file: null,
  updateSupport: false
})

const importResult = ref(null)

// 下载模板
const downloadTemplate = () => {
  window.open('/admin-api/agent/logistics-zone/get-import-template', '_blank')
}

// 文件选择
const handleFileChange = (file) => {
  importForm.file = file.raw
}

// 执行导入
const handleImport = async () => {
  if (!importForm.productId) {
    ElMessage.warning('请选择物流产品')
    return
  }
  if (!importForm.file) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    importing.value = true
    const result = await importLogisticsZones(
      importForm.productId,
      importForm.file,
      importForm.updateSupport
    )

    importResult.value = result.data
    showImportDialog.value = false
    showResultDialog.value = true

    // 刷新列表
    emit('refresh')

  } catch (error) {
    ElMessage.error('导入失败：' + error.message)
  } finally {
    importing.value = false
  }
}

const emit = defineEmits(['refresh'])
</script>
```

### 2. 导入结果组件

```vue
<template>
  <div class="import-result">
    <!-- 统计信息 -->
    <div class="result-summary">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-statistic title="创建成功" :value="result.createSuccessCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="更新成功" :value="result.updateSuccessCount" />
        </el-col>
        <el-col :span="8">
          <el-statistic title="失败条数" :value="result.failureCount" />
        </el-col>
      </el-row>
    </div>

    <!-- 失败详情 -->
    <div v-if="result.failureCount > 0" class="failure-details mt-4">
      <h4>失败详情</h4>
      <el-table :data="result.failureData" border>
        <el-table-column prop="row" label="行号" width="80" />
        <el-table-column prop="data.countryCode" label="国家编码" width="100" />
        <el-table-column prop="data.zoneCode" label="分区编码" width="120" />
        <el-table-column prop="data.zoneName" label="分区名称" width="150" />
        <el-table-column prop="reason" label="失败原因" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
defineProps({
  result: {
    type: Object,
    required: true
  }
})
</script>
```

## 🔧 API 方法定义

```javascript
// api/agent/logistics-zone.js
import request from '@/utils/request'

// 导入物流分区
export function importLogisticsZones(productId, file, updateSupport = false) {
  const formData = new FormData()
  formData.append('productId', productId)
  formData.append('file', file)
  formData.append('updateSupport', updateSupport)

  return request({
    url: '/agent/logistics-zone/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载导入模板
export function downloadImportTemplate() {
  return request({
    url: '/agent/logistics-zone/get-import-template',
    method: 'get',
    responseType: 'blob'
  })
}
```

## ⚠️ 注意事项

### 权限控制
- 确保用户有 `agent:logistics-zone:import` 权限
- 导入功能需要相应的角色权限

### 文件限制
- 文件大小：建议限制上传文件大小不超过 10MB
- 文件格式：只支持 `.xlsx` 和 `.xls` 格式
- 文件内容：Excel 第一行必须是标题行

### 数据验证
- **必填字段**：国家编码、分区编码、分区名称、状态
- **状态值**：只能是 0（禁用）或 1（启用）
- **限制类型**：只能是 NORMAL、REMOTE_FEE、FORBIDDEN
- **邮编格式**：必须是有效的 JSON 数组格式

### 错误处理
- 网络错误：显示友好的错误提示
- 业务错误：根据后端返回的错误码显示相应信息
- 文件格式错误：前端预先验证文件类型和大小

### 用户体验
- 提供清晰的进度提示和结果反馈
- 失败数据显示详细的错误原因和行号
- 支持下载导入模板，方便用户填写

## 🎯 完整功能流程

1. **准备阶段**
   - 用户点击"导入分区"按钮
   - 弹出导入对话框

2. **模板下载**
   - 用户点击"下载导入模板"
   - 获取标准格式的 Excel 模板

3. **数据准备**
   - 用户按照模板格式填写分区数据
   - 保存为 Excel 文件

4. **文件上传**
   - 选择物流产品
   - 上传填写好的 Excel 文件
   - 设置是否允许更新已存在数据

5. **数据导入**
   - 点击确定导入，显示进度
   - 后端处理数据验证和导入

6. **结果展示**
   - 显示导入结果统计
   - 如有失败数据，显示详细错误信息
   - 刷新分区列表

## 🔍 测试建议

### 功能测试
- 测试模板下载功能
- 测试正常数据导入
- 测试重复数据处理
- 测试数据验证规则

### 异常测试
- 测试文件格式错误
- 测试必填字段缺失
- 测试数据格式错误
- 测试网络异常情况

### 性能测试
- 测试大文件导入性能
- 测试并发导入情况

---

**文档版本：** v1.0
**创建日期：** 2025-01-20
**更新日期：** 2025-01-20
**维护人员：** 后端开发团队
