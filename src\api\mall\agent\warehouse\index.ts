import request from '@/config/axios'

// 代购平台仓库 VO
export interface WarehouseVO {
  id: number // 仓库编号
  code: string // 仓库编码
  name: string // 仓库名称
  address: string // 仓库地址
  phone: string // 联系电话
  recipient: string // 收件人
  freeDays: number // 免费存放天数
  sort: number // 排序
  remark: string // 备注
  status: number // 开启状态
  defaultStatus: boolean // 是否默认
}

// 代购平台仓库 API
export const WarehouseApi = {
  // 查询代购平台仓库分页
  getWarehousePage: async (params: any) => {
    return await request.get({ url: `/agent/warehouse/page`, params })
  },

  // 查询代购平台仓库详情
  getWarehouse: async (id: number) => {
    return await request.get({ url: `/agent/warehouse/get?id=` + id })
  },
  // 查询代购平台仓库列表
  getSimpleWarehouseList: async () => {
    return await request.get({ url: `/agent/warehouse/list-all-simple`})
  },

  // 新增代购平台仓库
  createWarehouse: async (data: WarehouseVO) => {
    return await request.post({ url: `/agent/warehouse/create`, data })
  },

  // 修改代购平台仓库
  updateWarehouse: async (data: WarehouseVO) => {
    return await request.put({ url: `/agent/warehouse/update`, data })
  },

  // 删除代购平台仓库
  deleteWarehouse: async (id: number) => {
    return await request.delete({ url: `/agent/warehouse/delete?id=` + id })
  },

  // 导出代购平台仓库 Excel
  exportWarehouse: async (params) => {
    return await request.download({ url: `/agent/warehouse/export-excel`, params })
  }
}