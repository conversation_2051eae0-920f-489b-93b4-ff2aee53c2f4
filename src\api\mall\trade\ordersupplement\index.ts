import request from '@/config/axios'

// 订单补款单 VO
export interface OrderSupplementVO {
  id: number // 编号
  no: string // 补款单流水号
  userId: number // 用户编号
  orderType: number // 单据类型
  orderId: number // 订单编号
  originalPayPrice: number // 原支付金额
  actualPayPrice: number // 实际应付金额
  payPrice: number // 需补款金额
  reason: string // 补款原因
  status: number // 支付状态
  payOrderId: number // 支付订单编号
  payChannelCode: string // 支付成功的支付渠道
  payTime: Date // 订单支付时间
  remark: string // 备注
}

// 订单补款单 API
export const OrderSupplementApi = {
  // 查询订单补款单分页
  getOrderSupplementPage: async (params: any) => {
    return await request.get({ url: `/trade/order-supplement/page`, params })
  },

  // 查询订单补款单详情
  getOrderSupplement: async (id: number) => {
    return await request.get({ url: `/trade/order-supplement/get?id=` + id })
  },

  // 新增订单补款单
  createOrderSupplement: async (data: OrderSupplementVO) => {
    return await request.post({ url: `/trade/order-supplement/create`, data })
  },

  // 修改订单补款单
  updateOrderSupplement: async (data: OrderSupplementVO) => {
    return await request.put({ url: `/trade/order-supplement/update`, data })
  },

  // 删除订单补款单
  deleteOrderSupplement: async (id: number) => {
    return await request.delete({ url: `/trade/order-supplement/delete?id=` + id })
  },

  // 导出订单补款单 Excel
  exportOrderSupplement: async (params) => {
    return await request.download({ url: `/trade/order-supplement/export-excel`, params })
  }
}