import request from '@/config/axios'

// 获得国家
export const getCountry = async () => {
  return await request.get({ url: '/system/area/country' })
}

// 获得地区树
export const getAreaTree = async () => {
  return await request.get({ url: '/system/area/tree' })
}
// 根据国家编码获得地区树
export const getAreaTreeByCountryCode = async (countryCode: string) => {
  return await request.get({ url: '/system/area/treeByCountryCode?countryCode=' + countryCode })
}

// 获得 IP 对应的地区名
export const getAreaByIp = async (ip: string) => {
  return await request.get({ url: '/system/area/get-by-ip?ip=' + ip })
}
