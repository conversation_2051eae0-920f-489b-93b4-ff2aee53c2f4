<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="国家编码" prop="countryCode">
        <el-input v-model="formData.countryCode" placeholder="请输入国家编码" />
      </el-form-item>
      <el-form-item label="产品编号" prop="productId">
        <el-input v-model="formData.productId" placeholder="请输入产品编号" />
      </el-form-item>
      <el-form-item label="分区编码" prop="zoneCode">
        <el-input v-model="formData.zoneCode" placeholder="请输入分区编码" />
      </el-form-item>
      <el-form-item label="分区名称" prop="zoneName">
        <el-input v-model="formData.zoneName" placeholder="请输入分区名称" />
      </el-form-item>
      <el-form-item label="邮编配置" prop="postalCodes">
        <el-input v-model="formData.postalCodes" placeholder="请输入邮编配置" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LogisticsZoneApi, LogisticsZoneVO } from '@/api/mall/agent/logisticsZone'

/** 代购物流国家分区 表单 */
defineOptions({ name: 'LogisticsZoneForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined as number | undefined,
  countryCode: undefined as string | undefined,
  productId: undefined as number | undefined,
  zoneCode: undefined as string | undefined,
  zoneName: undefined as string | undefined,
  postalCodes: undefined as string | undefined,
  status: undefined as number | undefined
})
const formRules = reactive({
  countryCode: [{ required: true, message: '国家编码不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, productId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 如果传入了productId，设置到表单数据中
  if (productId) {
    formData.value.productId = productId
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LogisticsZoneApi.getLogisticsZone(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LogisticsZoneVO
    if (formType.value === 'create') {
      await LogisticsZoneApi.createLogisticsZone(data)
      message.success(t('common.createSuccess'))
    } else {
      await LogisticsZoneApi.updateLogisticsZone(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    countryCode: undefined,
    productId: undefined,
    zoneCode: undefined,
    zoneName: undefined,
    postalCodes: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>
