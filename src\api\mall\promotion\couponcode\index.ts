import request from '@/config/axios'

// 优惠券动态码 VO
export interface CouponCodeVO {
  id: number // 编号
  templateId: number // 优惠劵模板编号
  code: string // 优惠码
  status: number // 状态
  userId: number // 用户编号
  businessType: number // 业务类型
  businessId: number // 业务ID
  discountAmount: number // 优惠金额（分）
  useTime: Date // 使用时间
  expireTime: Date // 过期时间
}

// 优惠券动态码 API
export const CouponCodeApi = {
  // 查询优惠券动态码分页
  getCouponCodePage: async (params: any) => {
    return await request.get({ url: `/promotion/coupon-code/page`, params })
  },

  // 查询优惠券动态码详情
  getCouponCode: async (id: number) => {
    return await request.get({ url: `/promotion/coupon-code/get?id=` + id })
  },

  // 新增优惠券动态码
  createCouponCode: async (data: CouponCodeVO) => {
    return await request.post({ url: `/promotion/coupon-code/create`, data })
  },

  // 修改优惠券动态码
  updateCouponCode: async (data: CouponCodeVO) => {
    return await request.put({ url: `/promotion/coupon-code/update`, data })
  },

  // 删除优惠券动态码
  deleteCouponCode: async (id: number) => {
    return await request.delete({ url: `/promotion/coupon-code/delete?id=` + id })
  },

  // 导出优惠券动态码 Excel
  exportCouponCode: async (params) => {
    return await request.download({ url: `/promotion/coupon-code/export-excel`, params })
  }
}