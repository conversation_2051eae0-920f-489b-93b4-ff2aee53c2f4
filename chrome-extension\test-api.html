<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔧 API测试工具</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="apiUrl">API地址:</label>
            <input type="url" id="apiUrl" value="http://localhost:81/admin-api/product/spu/create-product-by-url" required>
        </div>
        
        <div class="form-group">
            <label for="token">Token:</label>
            <input type="text" id="token" placeholder="请输入Bearer Token" required>
        </div>
        
        <div class="form-group">
            <label for="tenantId">租户ID:</label>
            <input type="text" id="tenantId" placeholder="请输入租户ID" required>
        </div>
        
        <div class="form-group">
            <label for="testUrl">测试商品URL:</label>
            <input type="url" id="testUrl" value="https://detail.1688.com/offer/123456789.html" required>
        </div>
        
        <div class="form-group">
            <label for="language">语言:</label>
            <select id="language">
                <option value="zh">中文</option>
                <option value="en">英文</option>
            </select>
        </div>
        
        <button type="submit">🚀 测试API</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiUrl = document.getElementById('apiUrl').value;
            const token = document.getElementById('token').value;
            const tenantId = document.getElementById('tenantId').value;
            const testUrl = document.getElementById('testUrl').value;
            const language = document.getElementById('language').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '⏳ 测试中...';
            
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`,
                        'tenant-id': tenantId
                    },
                    body: JSON.stringify({
                        url: testUrl,
                        lang: language
                    })
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ 测试成功！\n\n状态码: ${response.status}\n响应数据:\n${JSON.stringify(responseData, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ 测试失败！\n\n状态码: ${response.status}\n错误信息:\n${JSON.stringify(responseData, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 网络错误！\n\n错误信息: ${error.message}\n\n可能的原因:\n1. API地址不正确\n2. 后端服务未运行\n3. CORS配置问题\n4. 网络连接问题`;
            }
        });
    </script>
</body>
</html>
