import request from '@/config/axios'

// 国际货运公司 VO
export interface TransportCompanyVO {
  id: number // 编号
  code: string // 运输公司编码
  name: string // 运输公司名称
  logo: string // 运输公司 logo
  sort: number // 排序
  status: number // 状态
}

// 国际货运公司 API
export const TransportCompanyApi = {
  // 查询国际货运公司分页
  getTransportCompanyPage: async (params: any) => {
    return await request.get({ url: `/agent/transport-company/page`, params })
  },

    // 查询国际货运公司分页
    getTransportCompanyList: async () => {
      return await request.get({ url: `/agent/transport-company/simple-list` })
    },

  // 查询国际货运公司详情
  getTransportCompany: async (id: number) => {
    return await request.get({ url: `/agent/transport-company/get?id=` + id })
  },

  // 新增国际货运公司
  createTransportCompany: async (data: TransportCompanyVO) => {
    return await request.post({ url: `/agent/transport-company/create`, data })
  },

  // 修改国际货运公司
  updateTransportCompany: async (data: TransportCompanyVO) => {
    return await request.put({ url: `/agent/transport-company/update`, data })
  },

  // 删除国际货运公司
  deleteTransportCompany: async (id: number) => {
    return await request.delete({ url: `/agent/transport-company/delete?id=` + id })
  },

  // 导出国际货运公司 Excel
  exportTransportCompany: async (params) => {
    return await request.download({ url: `/agent/transport-company/export-excel`, params })
  }
}