import request from '@/config/axios'

// 代购物流国家分区 VO
export interface LogisticsZoneVO {
  id: number // 分类编号
  countryCode: string // 国家编码
  productId: number // 产品编号
  zoneCode: string // 分区编码
  zoneName: string // 分区名称
  postalCodes: string // 邮编配置
  status: number // 开启状态
}

// 代购物流国家分区 API
export const LogisticsZoneApi = {
  // 查询代购物流国家分区分页
  getLogisticsZonePage: async (params: any) => {
    return await request.get({ url: `/agent/logistics-zone/page`, params })
  },

  // 查询代购物流国家分区详情
  getLogisticsZone: async (id: number) => {
    return await request.get({ url: `/agent/logistics-zone/get?id=` + id })
  },

  // 新增代购物流国家分区
  createLogisticsZone: async (data: LogisticsZoneVO) => {
    return await request.post({ url: `/agent/logistics-zone/create`, data })
  },

  // 修改代购物流国家分区
  updateLogisticsZone: async (data: LogisticsZoneVO) => {
    return await request.put({ url: `/agent/logistics-zone/update`, data })
  },

  // 删除代购物流国家分区
  deleteLogisticsZone: async (id: number) => {
    return await request.delete({ url: `/agent/logistics-zone/delete?id=` + id })
  },

  // 导出代购物流国家分区 Excel
  exportLogisticsZone: async (params) => {
    return await request.download({ url: `/agent/logistics-zone/export-excel`, params })
  },

  // 下载导入模板
  downloadImportTemplate: async () => {
    return await request.download({ url: `/agent/logistics-zone/get-import-template` })
  },

  // 导入物流分区数据
  importLogisticsZone: async (productId: number, file: File, updateSupport: boolean = false) => {
    const formData = new FormData()
    formData.append('productId', productId.toString())
    formData.append('file', file)
    formData.append('updateSupport', updateSupport.toString())

    return await request.upload({
      url: `/agent/logistics-zone/import`,
      data: formData
    })
  },
}
