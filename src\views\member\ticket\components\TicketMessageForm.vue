<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px">
      <el-table-column label="序号" type="index" width="100" />
       <el-table-column label="回复类型" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.messageType`" :rules="formRules.messageType" class="mb-0px!">
            <el-select v-model="row.messageType" placeholder="请选择回复类型">
                <el-option
                  v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_MESSAGE_TYPE)"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="回复内容" min-width="400">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.content`" :rules="formRules.content" class="mb-0px!">
            <!-- <Editor v-model="row.content" height="150px" /> -->
            <el-input
          v-model="row.content"
          placeholder="请输入描述"
          type="textarea"
          :rows="3"
        />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="附件URL列表" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.attachmentUrls`" :rules="formRules.attachmentUrls" class="mb-0px!">
            <el-input v-model="row.attachmentUrls" placeholder="请输入附件URL列表" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="回复人编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.replierId`" :rules="formRules.replierId" class="mb-0px!">
            <el-input v-model="row.replierId" placeholder="请输入回复人编号" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="回复人名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.replierName`" :rules="formRules.replierName" class="mb-0px!">
            <el-input v-model="row.replierName" placeholder="请输入回复人名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <el-button @click="handleDelete($index)" link>—</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加用户工单信息</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TicketApi } from '@/api/member/ticket'

const props = defineProps<{
  ticketId: undefined // 工单编号（主表的关联字段）
}>()
const formLoading = ref(false) // 表单的加载中
const formData = ref([])
const formRules = reactive({
  ticketId: [{ required: true, message: '工单编号不能为空', trigger: 'blur' }],
  messageType: [{ required: true, message: '回复类型不能为空', trigger: 'change' }],
  content: [{ required: true, message: '回复内容不能为空', trigger: 'blur' }],
  replierId: [{ required: true, message: '回复人编号不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.ticketId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      formData.value = await TicketApi.getTicketMessageListByTicketId(val)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row = {
    id: undefined,
    ticketId: undefined,
    messageType: undefined,
    content: undefined,
    attachmentUrls: undefined,
    replierId: undefined,
    replierName: undefined
  }
  row.ticketId = props.ticketId
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index) => {
  formData.value.splice(index, 1)
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

defineExpose({ validate, getData })
</script>