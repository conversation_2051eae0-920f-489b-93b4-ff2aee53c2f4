import request from '@/config/axios'

// 代购服务国家 VO
export interface CountryVO {
  id: number // 编号
  code: string // 国家编码
  flagUrl: string // 图标地址
  nameZh: string // 中文名称
  nameEn: string // 英文名称
  nameFr: string // 法语名称
  nameDe: string // 德语名称
  nameEs: string // 西班牙语名称
  nameAr: string // 阿拉伯语名称
  sort: number // 排序
  hot: boolean // 热门
  status: number // 开启状态
}

// 代购服务国家 API
export const CountryApi = {
  // 查询代购服务国家分页
  getCountryPage: async (params: any) => {
    return await request.get({ url: `/agent/country/page`, params })
  },

  // 查询代购服务国家详情
  getCountry: async (id: number) => {
    return await request.get({ url: `/agent/country/get?id=` + id })
  },

  // 新增代购服务国家
  createCountry: async (data: CountryVO) => {
    return await request.post({ url: `/agent/country/create`, data })
  },

  // 修改代购服务国家
  updateCountry: async (data: CountryVO) => {
    return await request.put({ url: `/agent/country/update`, data })
  },

  // 删除代购服务国家
  deleteCountry: async (id: number) => {
    return await request.delete({ url: `/agent/country/delete?id=` + id })
  },

  // 导出代购服务国家 Excel
  exportCountry: async (params) => {
    return await request.download({ url: `/agent/country/export-excel`, params })
  },

  // 查询国家简单列表
  getCountrySimpleList: async () => {
    return await request.get({ url: `/agent/country/list-all-simple` })
  },
}
