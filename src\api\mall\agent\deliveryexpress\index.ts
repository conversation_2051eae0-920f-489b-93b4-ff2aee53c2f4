import request from '@/config/axios'

// 代购快递公司 VO
export interface DeliveryExpressVO {
  id: number // 编号
  code: string // 快递公司编码
  name: string // 快递公司名称
  logo: string // 快递公司 logo
  sort: number // 排序
  status: number // 状态
}

// 代购快递公司 API
export const DeliveryExpressApi = {
  // 查询代购快递公司分页
  getDeliveryExpressPage: async (params: any) => {
    return await request.get({ url: `/agent/delivery-express/page`, params })
  },

  // 查询代购快递公司详情
  getDeliveryExpress: async (id: number) => {
    return await request.get({ url: `/agent/delivery-express/get?id=` + id })
  },

  // 新增代购快递公司
  createDeliveryExpress: async (data: DeliveryExpressVO) => {
    return await request.post({ url: `/agent/delivery-express/create`, data })
  },

  // 修改代购快递公司
  updateDeliveryExpress: async (data: DeliveryExpressVO) => {
    return await request.put({ url: `/agent/delivery-express/update`, data })
  },

  // 删除代购快递公司
  deleteDeliveryExpress: async (id: number) => {
    return await request.delete({ url: `/agent/delivery-express/delete?id=` + id })
  },

  // 导出代购快递公司 Excel
  exportDeliveryExpress: async (params) => {
    return await request.download({ url: `/agent/delivery-express/export-excel`, params })
  }
}