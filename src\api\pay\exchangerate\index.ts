import request from '@/config/axios'

// 汇率 VO
export interface ExchangeRateVO {
  id: number // 日志主键
  currency: string // 目标货币
  symbol: string // 货币符号
  currencyName: string // 货币名称
  rate: number // 汇率值
  rateSource: string // 汇率来源
  defaultStatus: boolean // 是否默认
  status: number // 开启状态
  memo: string // 备注
}

// 汇率 API
export const ExchangeRateApi = {
  // 查询汇率分页
  getExchangeRatePage: async (params: any) => {
    return await request.get({ url: `/pay/exchange-rate/page`, params })
  },

  // 查询汇率详情
  getExchangeRate: async (id: number) => {
    return await request.get({ url: `/pay/exchange-rate/get?id=` + id })
  },
  

  // 新增汇率
  createExchangeRate: async (data: ExchangeRateVO) => {
    return await request.post({ url: `/pay/exchange-rate/create`, data })
  },

  // 修改汇率
  updateExchangeRate: async (data: ExchangeRateVO) => {
    return await request.put({ url: `/pay/exchange-rate/update`, data })
  },

  // 删除汇率
  deleteExchangeRate: async (id: number) => {
    return await request.delete({ url: `/pay/exchange-rate/delete?id=` + id })
  },

  // 导出汇率 Excel
  exportExchangeRate: async (params) => {
    return await request.download({ url: `/pay/exchange-rate/export-excel`, params })
  }
}