import request from '@/config/axios'

// 广告跟踪 VO
export interface AdTrackingVO {
  id: number // 编号
  userId: number // 用户编号
  sessionId: string // 会话ID
  utmSource: string // 广告来源
  utmMedium: string // 广告媒介
  utmCampaign: string // 广告活动
  utmTerm: string // 广告关键词
  utmContent: string // 广告内容
  gclid: string // Google点击ID
  fbclid: string // Facebook点击ID
  landingPage: string // 着陆页面
  referrer: string // 来源页面
  userAgent: string // 用户代理
  ipAddress: string // IP地址
  isConverted: number // 是否已转化
  conversionType: string // 转化类型
  conversionValue: string // 转化价值
  conversionTime: string // 转化时间
}

// 广告跟踪 API
export const AdTrackingApi = {
  // 查询广告跟踪分页
  getAdTrackingPage: async (params: any) => {
    return await request.get({ url: `/member/ad-tracking/page`, params })
  },

  // 查询广告跟踪详情
  getAdTracking: async (id: number) => {
    return await request.get({ url: `/member/ad-tracking/get?id=` + id })
  },

  // 新增广告跟踪
  createAdTracking: async (data: AdTrackingVO) => {
    return await request.post({ url: `/member/ad-tracking/create`, data })
  },

  // 修改广告跟踪
  updateAdTracking: async (data: AdTrackingVO) => {
    return await request.put({ url: `/member/ad-tracking/update`, data })
  },

  // 删除广告跟踪
  deleteAdTracking: async (id: number) => {
    return await request.delete({ url: `/member/ad-tracking/delete?id=` + id })
  },

  // 导出广告跟踪 Excel
  exportAdTracking: async (params) => {
    return await request.download({ url: `/member/ad-tracking/export-excel`, params })
  }
}