# 代购物流产品价格规则导入API文档

## 概述

本文档描述了代购物流产品价格规则Excel导入功能的前后端接口规范，用于前端开发人员对接使用。

## 功能说明

- **导入场景**：在物流产品详情页面，为当前选中的产品批量导入价格规则
- **导入方式**：通过Excel文件上传，支持批量创建和更新价格规则
- **数据校验**：完整的字段校验和业务规则校验
- **错误处理**：详细的错误信息反馈，精确到行号

## API接口

### 1. 下载导入模板

#### 接口信息
- **URL**: `GET /admin-api/agent/logistics-product/logistics-product-price/get-import-template`
- **描述**: 下载Excel导入模板文件
- **权限**: 无需特殊权限

#### 请求参数
无

#### 响应
- **Content-Type**: `application/vnd.ms-excel`
- **文件名**: `代购物流产品价格规则导入模板.xls`
- **说明**: 直接下载Excel文件，包含示例数据和字段说明

#### 前端实现示例
```javascript
// 下载导入模板
const downloadTemplate = () => {
  const url = '/admin-api/agent/logistics-product/logistics-product-price/get-import-template'
  const link = document.createElement('a')
  link.href = url
  link.download = '代购物流产品价格规则导入模板.xls'
  link.click()
}
```

### 2. 导入价格规则

#### 接口信息
- **URL**: `POST /admin-api/agent/logistics-product/logistics-product-price/import`
- **描述**: 导入Excel文件中的价格规则数据
- **权限**: `agent:logistics-product:import`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | Long | 是 | 产品编号，当前选中的物流产品ID |
| file | MultipartFile | 是 | Excel文件 |
| updateSupport | Boolean | 否 | 是否支持更新，默认false |

#### 请求示例
```javascript
// FormData方式上传
const importPriceRules = (productId, file, updateSupport = false) => {
  const formData = new FormData()
  formData.append('productId', productId)
  formData.append('file', file)
  formData.append('updateSupport', updateSupport)
  
  return request({
    url: '/admin-api/agent/logistics-product/logistics-product-price/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

#### 响应格式
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "createSuccessCount": 5,
    "updateSuccessCount": 2,
    "failureCount": 1,
    "failureData": [
      {
        "row": 8,
        "data": {
          "countryCode": "US",
          "zoneCode": "",
          "chargeType": "WEIGHT",
          "priceType": "INCREMENTAL",
          "firstUnit": 500,
          "firstPrice": 10500
        },
        "reason": "第8行：续重/续件单位必须大于0"
      }
    ]
  }
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| createSuccessCount | Integer | 创建成功的数量 |
| updateSuccessCount | Integer | 更新成功的数量 |
| failureCount | Integer | 导入失败的数量 |
| failureData | Array | 失败数据详情 |
| failureData[].row | Integer | 失败的Excel行号 |
| failureData[].data | Object | 失败的数据内容 |
| failureData[].reason | String | 失败原因 |

## Excel模板格式

### 字段列表
| 列序号 | 字段名 | 是否必填 | 数据类型 | 示例值 | 说明 |
|--------|--------|----------|----------|--------|------|
| A | 国家编码 | 是 | String | US | ISO 3166-1 alpha-2标准 |
| B | 分区编码 | 否 | String | Zone1 | 为空表示全国统一价格 |
| C | 时效 | 否 | String | 12-20天 | 描述性文字 |
| D | 计费方式 | 是 | String | WEIGHT | WEIGHT/VOLUME/PIECE |
| E | 价格类型 | 是 | String | INCREMENTAL | TIERED/INCREMENTAL |
| F | 首重(g)/首件数量 | 是 | Integer | 500 | 单位：克或件 |
| G | 首重/首件价格(分) | 是 | Integer | 10500 | 单位：分 |
| H | 续重(g)/续件单位 | 否 | Integer | 200 | 递增模式必填 |
| I | 续重/续件价格(分) | 否 | Integer | 2940 | 递增模式必填 |
| J | 最小重量(g) | 否 | Integer | 100 | 单位：克 |
| K | 最大重量(g) | 否 | Integer | 30000 | 单位：克 |
| L | 阶梯价格配置JSON | 否 | String | 见下方示例 | 阶梯模式必填 |
| M | 燃油费率 | 否 | Decimal | 0.15 | 小数格式 |
| N | 挂号费(分) | 否 | Integer | 0 | 单位：分 |
| O | 操作费(分) | 否 | Integer | 0 | 单位：分 |
| P | 服务费(分) | 否 | Integer | 0 | 单位：分 |
| Q | 清关费(分) | 否 | Integer | 0 | 单位：分 |
| R | 是否预收关税 | 否 | Integer | 0 | 0-否，1-是 |
| S | 关税税率 | 否 | Decimal | 0.00 | 小数格式 |
| T | 生效日期 | 否 | DateTime | 2024-01-01 00:00:00 | 日期时间格式 |
| U | 失效日期 | 否 | DateTime | 2024-12-31 23:59:59 | 日期时间格式 |

### 阶梯价格配置JSON格式
```json
[
  {
    "tierStart": 0,
    "tierEnd": 500,
    "unitPrice": 12000,
    "description": "0-500g"
  },
  {
    "tierStart": 501,
    "tierEnd": 1000,
    "unitPrice": 8000,
    "description": "501-1000g"
  },
  {
    "tierStart": 1001,
    "tierEnd": null,
    "unitPrice": 6000,
    "description": "1001g以上"
  }
]
```

## 前端页面集成建议

### 1. 页面布局
建议在物流产品详情页面的价格规则列表上方添加导入功能区域：

```vue
<template>
  <div class="price-import-section">
    <!-- 导入操作区 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="12">
        <el-button type="primary" @click="downloadTemplate">
          <i class="el-icon-download"></i> 下载导入模板
        </el-button>
      </el-col>
      <el-col :span="12" class="text-right">
        <el-upload
          ref="uploadRef"
          :action="importUrl"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :show-file-list="false"
          accept=".xls,.xlsx">
          <el-button type="success">
            <i class="el-icon-upload"></i> 导入价格规则
          </el-button>
        </el-upload>
        <el-checkbox v-model="updateSupport" class="ml-2">
          支持更新已存在数据
        </el-checkbox>
      </el-col>
    </el-row>
    
    <!-- 导入结果显示 -->
    <el-alert
      v-if="importResult"
      :type="importResult.failureCount > 0 ? 'warning' : 'success'"
      :title="getResultTitle()"
      :description="getResultDescription()"
      show-icon
      :closable="false"
      class="mb-4">
    </el-alert>
  </div>
</template>
```

### 2. 核心方法实现
```javascript
export default {
  data() {
    return {
      productId: null, // 当前产品ID
      updateSupport: false,
      importResult: null,
      importUrl: '/admin-api/agent/logistics-product/logistics-product-price/import'
    }
  },
  computed: {
    uploadData() {
      return {
        productId: this.productId,
        updateSupport: this.updateSupport
      }
    }
  },
  methods: {
    // 下载模板
    downloadTemplate() {
      const url = '/admin-api/agent/logistics-product/logistics-product-price/get-import-template'
      window.open(url)
    },
    
    // 上传前校验
    beforeUpload(file) {
      const isExcel = file.type === 'application/vnd.ms-excel' || 
                     file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!')
        return false
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }
      
      this.importResult = null
      return true
    },
    
    // 上传成功
    onUploadSuccess(response) {
      if (response.code === 0) {
        this.importResult = response.data
        this.$message.success('导入完成!')
        // 刷新价格规则列表
        this.refreshPriceList()
      } else {
        this.$message.error(response.msg || '导入失败')
      }
    },
    
    // 上传失败
    onUploadError(error) {
      this.$message.error('文件上传失败: ' + error.message)
    },
    
    // 获取结果标题
    getResultTitle() {
      if (!this.importResult) return ''
      const { createSuccessCount, updateSuccessCount, failureCount } = this.importResult
      return `导入完成：成功创建${createSuccessCount}条，更新${updateSuccessCount}条，失败${failureCount}条`
    },
    
    // 获取结果描述
    getResultDescription() {
      if (!this.importResult || this.importResult.failureCount === 0) return ''
      return this.importResult.failureData.map(item => 
        `第${item.row}行：${item.reason}`
      ).join('；')
    }
  }
}
```

## 错误处理

### 常见错误类型
1. **文件格式错误**：只支持.xls和.xlsx格式
2. **必填字段为空**：国家编码、计费方式、价格类型等
3. **数据格式错误**：数值字段格式不正确
4. **业务规则校验失败**：如递增模式下续重信息缺失
5. **唯一性约束冲突**：同一产品同一国家同一分区已存在价格规则

### 错误处理建议
1. **前端校验**：文件格式、大小等基础校验
2. **后端校验**：完整的业务规则校验
3. **错误展示**：清晰的错误信息展示，帮助用户快速定位问题
4. **操作指引**：提供修改建议和重新导入的操作指引

## 注意事项

1. **产品ID自动设置**：导入时会自动使用当前选中的产品ID，Excel中无需填写
2. **默认值处理**：排序默认为0，状态默认为启用
3. **更新模式**：建议提供复选框让用户选择是否支持更新已存在数据
4. **权限控制**：确保用户具有`agent:logistics-product:import`权限
5. **文件大小限制**：建议限制在10MB以内
6. **导入后刷新**：导入成功后需要刷新价格规则列表显示最新数据
