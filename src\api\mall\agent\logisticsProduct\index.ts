import request from '@/config/axios'

// 代购物流公司产品 VO
export interface LogisticsProductVO {
  id: number // 编号
  companyId: number // 物流公司编号
  productCode: string // 产品编码
  channelCode: string // 渠道编码
  nameZh: string // 中文名称
  nameEn: string // 英文名称
  nameFr: string // 法语名称
  nameDe: string // 德语名称
  nameEs: string // 西班牙语名称
  nameAr: string // 阿拉伯语名称
  featuresZh: string // 中文特性描述
  featuresEn: string // 英文特性描述
  featuresFr: string // 法语特性描述
  featuresDe: string // 德语特性描述
  featuresEs: string // 西班牙语特性描述
  featuresAr: string // 阿拉伯语特性描述
  iconUrl: string // 产品图标
  taxInclude: boolean // 是否包税
  needVolumeCal: boolean // 是否计算体积重
  volumeBase: number // 体积重计算基数
  minWeight: number // 最小重量(g)
  maxWeight: number // 最大重量(g)
  dimensionRestrictionZh: string // 中文尺寸限制描述
  dimensionRestrictionEn: string // 英文尺寸限制描述
  dimensionRestrictionFr: string // 法语尺寸限制描述
  dimensionRestrictionDe: string // 德语尺寸限制描述
  dimensionRestrictionEs: string // 西班牙尺寸限制描述
  dimensionRestrictionAr: string // 阿拉伯尺寸限制描述
  volumeWeightRuleZh: string // 中文体积重量计费规则描述
  volumeWeightRuleEn: string // 英文体积重量计费规则描述
  volumeWeightRuleFr: string // 法语体积重量计费规则描述
  volumeWeightRuleDe: string // 德语体积重量计费规则描述
  volumeWeightRuleEs: string // 西班牙体积重量计费规则描述
  volumeWeightRuleAr: string // 阿拉伯体积重量计费规则描述
  minDeclareValue: number // 最小申报价值(分)
  maxDeclareValue: number // 最大申报价值(分)
  defaultDeclareType: string // 默认申报类型
  declarePerKg: number // 每公斤申报价值(分)
  declareRatio: number // 申报比例
  freeInsure: number // 免费保险
  iossEnabled: number // IOSS
  calculationFormula: string // 计算公式表达式
  categoryRestrictions: string // 分类限制配置
  sort: number // 排序
  status: number // 状态
}

// 代购物流公司产品 API
export const LogisticsProductApi = {
  // 查询代购物流公司产品分页
  getLogisticsProductPage: async (params: any) => {
    return await request.get({ url: `/agent/logistics-product/page`, params })
  },

  // 查询代购物流公司产品详情
  getLogisticsProduct: async (id: number) => {
    return await request.get({ url: `/agent/logistics-product/get?id=` + id })
  },

  // 新增代购物流公司产品
  createLogisticsProduct: async (data: LogisticsProductVO) => {
    return await request.post({ url: `/agent/logistics-product/create`, data })
  },

  // 修改代购物流公司产品
  updateLogisticsProduct: async (data: LogisticsProductVO) => {
    return await request.put({ url: `/agent/logistics-product/update`, data })
  },

  // 删除代购物流公司产品
  deleteLogisticsProduct: async (id: number) => {
    return await request.delete({ url: `/agent/logistics-product/delete?id=` + id })
  },

  // 下载代购物流产品价格规则导入模板
  downloadImportTemplate: async () => {
    return await request.download({ url: `/agent/logistics-product/logistics-product-price/get-import-template` })
  },

  // 导入代购物流产品价格规则
  importLogisticsProductPrice: async (productId: number, file: File, updateSupport: boolean = false) => {
    const formData = new FormData()
    formData.append('productId', productId.toString())
    formData.append('file', file)
    formData.append('updateSupport', updateSupport.toString())

    return await request.upload({
      url: `/agent/logistics-product/logistics-product-price/import`,
      data: formData
    })
  },
  
  // 导出代购物流公司产品 Excel
  exportLogisticsProduct: async (params) => {
    return await request.download({ url: `/agent/logistics-product/export-excel`, params })
  },

// ==================== 子表（代购物流产品价格规则） ====================

  // 获得代购物流产品价格规则分页
  getLogisticsProductPricePage: async (params) => {
    return await request.get({ url: `/agent/logistics-product/logistics-product-price/page`, params })
  },
  // 新增代购物流产品价格规则
  createLogisticsProductPrice: async (data) => {
    return await request.post({ url: `/agent/logistics-product/logistics-product-price/create`, data })
  },

  // 批量新增代购物流产品价格规则（多个国家）
  createLogisticsProductPriceBatch: async (data) => {
    return await request.post({ url: `/agent/logistics-product/logistics-product-price/create-batch`, data })
  },

  // 修改代购物流产品价格规则
  updateLogisticsProductPrice: async (data) => {
    return await request.put({ url: `/agent/logistics-product/logistics-product-price/update`, data })
  },

  // 删除代购物流产品价格规则
  deleteLogisticsProductPrice: async (id: number) => {
    return await request.delete({ url: `/agent/logistics-product/logistics-product-price/delete?id=` + id })
  },

  // 获得代购物流产品价格规则
  getLogisticsProductPrice: async (id: number) => {
    return await request.get({ url: `/agent/logistics-product/logistics-product-price/get?id=` + id })
  },
}
