import request from '@/config/axios'

export interface NoticeVO {
  id: number | undefined
  titleEn: string
  titleZh: string
  titleFr: string
  titleEs: string
  titleDe: string
  titleAr: string
  contentEn: string
  contentZh: string
  contentFr: string
  contentEs: string
  contentDe: string
  contentAr: string
  type: number
  // content: string
  status: number
  remark: string
  creator: string
  createTime: Date
  publishTime: Date
}

// 查询公告列表
export const getNoticePage = (params: PageParam) => {
  return request.get({ url: '/system/notice/page', params })
}

// 查询公告详情
export const getNotice = (id: number) => {
  return request.get({ url: '/system/notice/get?id=' + id })
}

// 新增公告
export const createNotice = (data: NoticeVO) => {
  return request.post({ url: '/system/notice/create', data })
}

// 修改公告
export const updateNotice = (data: NoticeVO) => {
  return request.put({ url: '/system/notice/update', data })
}

// 删除公告
export const deleteNotice = (id: number) => {
  return request.delete({ url: '/system/notice/delete?id=' + id })
}

// 推送公告
export const pushNotice = (id: number) => {
  return request.post({ url: '/system/notice/push?id=' + id })
}
