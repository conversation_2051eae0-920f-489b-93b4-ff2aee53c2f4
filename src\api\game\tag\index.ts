import request from '@/config/axios'

// 游戏标签 VO
export interface TagVO {
  id: number // 编号
  name: string // 标签名称
}

// 游戏标签 API
export const TagApi = {
  // 查询游戏标签分页
  getTagPage: async (params: any) => {
    return await request.get({ url: `/game/tag/page`, params })
  },

  // 查询游戏标签详情
  getTag: async (id: number) => {
    return await request.get({ url: `/game/tag/get?id=` + id })
  },

  // 查询会员标签 - 精简信息列表
  getSimpleTagList : async () => {
    return await request.get({ url: `/game/tag/list-all-simple` })
  },

  // 新增游戏标签
  createTag: async (data: TagVO) => {
    return await request.post({ url: `/game/tag/create`, data })
  },

  // 修改游戏标签
  updateTag: async (data: TagVO) => {
    return await request.put({ url: `/game/tag/update`, data })
  },

  // 删除游戏标签
  deleteTag: async (id: number) => {
    return await request.delete({ url: `/game/tag/delete?id=` + id })
  },

  // 导出游戏标签 Excel
  exportTag: async (params) => {
    return await request.download({ url: `/game/tag/export-excel`, params })
  }
}

