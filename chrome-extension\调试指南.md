# 🔧 Chrome插件调试指南

## 🚨 常见问题解决方案

### 问题1: 点击按钮没有反应

**可能原因:**
1. 插件权限不足
2. API配置错误
3. 网络请求被阻止
4. JavaScript错误

**解决步骤:**

#### 步骤1: 检查插件权限
1. 进入 `chrome://extensions/`
2. 找到"商品快速添加助手"
3. 确认插件已启用
4. 点击"详细信息"
5. 检查"网站访问权限"是否正确

#### 步骤2: 重新加载插件
1. 在扩展管理页面点击"重新加载"按钮
2. 刷新购物网站页面
3. 重新测试

#### 步骤3: 检查控制台日志
1. 在购物网站页面按F12打开开发者工具
2. 切换到"Console"标签页
3. 点击插件按钮
4. 查看是否有错误信息

#### 步骤4: 检查Background Script日志
1. 进入 `chrome://extensions/`
2. 找到插件，点击"检查视图 service worker"
3. 在弹出的开发者工具中查看Console
4. 重新测试插件功能

### 问题2: API调用失败

**检查配置:**
1. 点击插件图标 → "插件设置"
2. 确认以下配置正确:
   - API地址: `http://localhost:80/admin-api/product/spu/create-product-by-url`
   - 访问令牌: 有效的Bearer Token
   - 租户ID: 正确的租户标识

**测试API连接:**
1. 在设置页面点击"测试API连接"
2. 查看是否返回成功信息

### 问题3: CORS跨域错误

**解决方案:**
1. 确认后端API支持CORS
2. 检查API响应头包含:
   ```
   Access-Control-Allow-Origin: *
   Access-Control-Allow-Methods: POST, OPTIONS
   Access-Control-Allow-Headers: Content-Type, Authorization, tenant-id
   ```

## 🔍 调试步骤

### 1. 基础检查
```bash
# 检查插件是否正确加载
1. chrome://extensions/ → 确认插件启用
2. 检查是否有错误提示
3. 重新加载插件
```

### 2. 网站兼容性检查
```javascript
// 在控制台运行以下代码检查网站适配
console.log('当前网站:', window.location.hostname);
console.log('是否支持:', ['taobao.com', 'tmall.com', 'jd.com', '1688.com', 'vip.com'].some(domain => 
  window.location.hostname.includes(domain)
));
```

### 3. 配置检查
```javascript
// 在background script控制台运行
chrome.storage.sync.get(['apiUrl', 'token', 'tenantId'], (result) => {
  console.log('当前配置:', result);
});
```

### 4. API测试
```bash
# 使用curl测试API
curl -X POST "http://localhost:80/admin-api/product/spu/create-product-by-url" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "tenant-id: YOUR_TENANT_ID" \
  -d '{"url":"https://test.com","lang":"zh"}'
```

## 📋 调试清单

### 插件安装检查
- [ ] 插件已正确加载
- [ ] 开发者模式已开启
- [ ] 插件权限已授予
- [ ] 没有错误提示

### 配置检查
- [ ] API地址格式正确
- [ ] Token有效且格式正确
- [ ] 租户ID正确
- [ ] 语言设置正确

### 网站检查
- [ ] 当前网站在支持列表中
- [ ] 页面已完全加载
- [ ] 浮动按钮已显示
- [ ] 商品信息可以正确提取

### API检查
- [ ] 后端服务正常运行
- [ ] API地址可访问
- [ ] CORS配置正确
- [ ] 认证信息有效

## 🛠️ 常用调试命令

### 查看插件状态
```javascript
// 在任意页面控制台运行
chrome.runtime.sendMessage({action: 'ping'}, (response) => {
  console.log('插件状态:', response);
});
```

### 手动触发添加商品
```javascript
// 在购物网站控制台运行
if (window.confirmAdd) {
  window.confirmAdd();
} else {
  console.log('confirmAdd函数未找到');
}
```

### 检查商品信息提取
```javascript
// 在购物网站控制台运行
const titleSelectors = [
  'h1[data-spm="1000983"]',
  '.tb-main-title',
  'h1.tb-item-title',
  '.sku-name'
];

titleSelectors.forEach(selector => {
  const element = document.querySelector(selector);
  if (element) {
    console.log(`找到标题 (${selector}):`, element.textContent.trim());
  }
});
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息:

1. **浏览器信息**
   - Chrome版本
   - 操作系统

2. **错误信息**
   - 控制台错误截图
   - Background script错误日志

3. **配置信息**
   - API地址 (隐藏敏感信息)
   - 当前测试的网站URL

4. **重现步骤**
   - 详细的操作步骤
   - 预期结果 vs 实际结果

---

**提示**: 大部分问题都是配置或权限相关，请仔细检查上述步骤！
