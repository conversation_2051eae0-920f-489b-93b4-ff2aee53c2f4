import request from '@/config/axios'

// 游戏评论 VO
export interface ReviewVO {
  id: number // 评论记录ID
  gameId: number // 游戏ID
  userId: number // 用户编号
  content: string // 评论内容
}

// 游戏评论 API
export const ReviewApi = {
  // 查询游戏评论分页
  getReviewPage: async (params: any) => {
    return await request.get({ url: `/game/review/page`, params })
  },

  // 查询游戏评论详情
  getReview: async (id: number) => {
    return await request.get({ url: `/game/review/get?id=` + id })
  },

  // 新增游戏评论
  createReview: async (data: ReviewVO) => {
    return await request.post({ url: `/game/review/create`, data })
  },

  // 修改游戏评论
  updateReview: async (data: ReviewVO) => {
    return await request.put({ url: `/game/review/update`, data })
  },

  // 删除游戏评论
  deleteReview: async (id: number) => {
    return await request.delete({ url: `/game/review/delete?id=` + id })
  },

  // 导出游戏评论 Excel
  exportReview: async (params) => {
    return await request.download({ url: `/game/review/export-excel`, params })
  }
}