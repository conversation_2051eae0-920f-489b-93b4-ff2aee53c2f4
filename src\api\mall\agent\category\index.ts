import request from '@/config/axios'

// 代购商品分类 VO
export interface CategoryVO {
  id: number // 编号
  parentId: number // 父编号
  code: string // 编码
  picUrl: string // 分类图
  nameZh: string // 中文名称
  nameEn: string // 英文名称
  nameFr: string // 法语名称
  nameDe: string // 德语名称
  nameEs: string // 西班牙语名称
  nameAr: string // 阿拉伯语名称
  remarkZh: string // 中文备注
  remarkEn: string // 英文备注
  remarkFr: string // 法语备注
  remarkDe: string // 德语备注
  remarkEs: string // 西班牙语备注
  remarkAr: string // 阿拉伯语备注
  sort: number // 排序
  hot: boolean // 热门
  status: number // 状态
}

// 代购商品分类 API
export const CategoryApi = {
  // 查询代购商品分类列表
  getCategoryList: async (params) => {
    return await request.get({ url: `/agent/category/list`, params })
  },

  // 查询代购商品分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/agent/category/get?id=` + id })
  },

  // 新增代购商品分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/agent/category/create`, data })
  },

  // 修改代购商品分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/agent/category/update`, data })
  },

  // 删除代购商品分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/agent/category/delete?id=` + id })
  },

  // 导出代购商品分类 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/agent/category/export-excel`, params })
  },
}
