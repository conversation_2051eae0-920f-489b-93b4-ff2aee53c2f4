<template>
  <Dialog v-model="dialogVisible" title="产品类别" width="800px">
    <div v-loading="loading" class="category-selector">
      <!-- 主分类列表 -->
      <div class="flex h-150">
        <!-- 左侧主分类 -->
        <div class="w-1/3 border-r border-gray-200">
          <div class="text-sm font-medium text-gray-700 px-3 py-2 border-b bg-gray-50">主分类</div>
          <div class="overflow-y-auto h-150">
            <div
              v-for="category in categoryTree"
              :key="category.id"
              class="flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
              :class="{ 'bg-blue-50 border-blue-200': selectedParentId === category.id }"
              @click="selectParentCategory(category)"
            >
              <span class="text-sm">{{ category.nameZh }}</span>
              <Icon icon="ep:arrow-right" class="text-gray-400" />
            </div>
          </div>
        </div>

        <!-- 右侧子分类 -->
        <div class="flex-1">
          <div class="text-sm font-medium text-gray-700 px-3 py-2 border-b bg-gray-50">
            {{ selectedParentCategory?.nameZh || '请选择主分类' }}
          </div>
          <div class="overflow-y-auto h-150">
            <div v-if="!selectedParentCategory" class="text-center py-6 text-gray-500">
              <div class="text-sm">请先选择左侧的主分类</div>
            </div>
            <div
              v-else-if="!selectedParentCategory.children?.length"
              class="text-center py-6 text-gray-500"
            >
              <div class="text-sm">该分类下暂无子分类</div>
            </div>
            <div v-else class="grid grid-cols-2 gap-1 p-2">
              <div
                v-for="child in selectedParentCategory.children"
                :key="child.id"
                class="flex items-center px-2 py-1 border border-gray-200 rounded hover:border-blue-300 cursor-pointer"
                :class="{ 'border-blue-500 bg-blue-50': selectedCategoryId === child.id }"
                @click="selectChildCategory(child)"
              >
                <el-checkbox
                  :model-value="selectedCategoryId === child.id"
                  @change="selectChildCategory(child)"
                  class="mr-1"
                />
                <div class="flex-1">
                  <div class="text-sm font-medium">{{ child.nameZh }}</div>
                  <div v-if="child.remarkZh" class="text-xs text-gray-500">
                    {{ child.remarkZh }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部统计和操作 -->
      <div class="flex justify-between items-center mt-3 pt-3 border-t">
        <div class="text-sm text-gray-600"> 已选择：{{ selectedCategoryId ? 1 : 0 }} 项 </div>
        <div>
          <el-button @click="handleClear">清空</el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { CategoryApi, type CategoryVO } from '@/api/mall/agent/category'

const message = useMessage()

// 弹窗控制
const dialogVisible = ref(false)
const loading = ref(false)

// 分类数据
const categoryTree = ref<CategoryTreeNode[]>([])
const selectedParentId = ref<number | undefined>()
const selectedParentCategory = ref<CategoryTreeNode | undefined>()
const selectedCategoryId = ref<number | undefined>()
const selectedCategory = ref<CategoryVO | undefined>()

// 类型定义
interface CategoryTreeNode extends CategoryVO {
  children?: CategoryTreeNode[]
}

// 组件属性
interface Props {
  modelValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined
})

// 事件定义
const emit = defineEmits<{
  'update:modelValue': [value: number | undefined]
  change: [category: CategoryVO | undefined]
}>()

/** 打开弹窗 */
const open = async (initialCategoryId?: number) => {
  dialogVisible.value = true
  loading.value = true

  try {
    await loadCategoryTree()

    // 如果有初始值，设置选中状态
    const categoryId = initialCategoryId || props.modelValue
    if (categoryId) {
      await setInitialSelection(categoryId)
    }
  } catch (error) {
    console.error('加载分类数据失败:', error)
    message.error('加载分类数据失败')
  } finally {
    loading.value = false
  }
}

/** 加载分类树数据 */
const loadCategoryTree = async () => {
  const response = await CategoryApi.getCategoryList({})
  const categories: CategoryVO[] = response

  // 构建树形结构
  const parentCategories = categories.filter((item) => item.parentId === 0)
  const childCategories = categories.filter((item) => item.parentId !== 0)

  categoryTree.value = parentCategories.map((parent) => {
    const children = childCategories.filter((child) => child.parentId === parent.id)
    return {
      ...parent,
      children: children.length > 0 ? children : undefined
    }
  })
}

/** 设置初始选中状态 */
const setInitialSelection = async (categoryId: number) => {
  // 在所有分类中查找目标分类
  for (const parent of categoryTree.value) {
    if (parent.children) {
      const child = parent.children.find((c) => c.id === categoryId)
      if (child) {
        selectedParentId.value = parent.id
        selectedParentCategory.value = parent
        selectedCategoryId.value = child.id
        selectedCategory.value = child
        break
      }
    }
  }
}

/** 选择主分类 */
const selectParentCategory = (category: CategoryTreeNode) => {
  selectedParentId.value = category.id
  selectedParentCategory.value = category

  // 如果当前选中的子分类不属于新选择的主分类，清空子分类选择
  if (selectedCategoryId.value) {
    const isChildOfNewParent = category.children?.some(
      (child) => child.id === selectedCategoryId.value
    )
    if (!isChildOfNewParent) {
      selectedCategoryId.value = undefined
      selectedCategory.value = undefined
    }
  }
}

/** 选择子分类 */
const selectChildCategory = (category: CategoryVO) => {
  if (selectedCategoryId.value === category.id) {
    // 取消选择
    selectedCategoryId.value = undefined
    selectedCategory.value = undefined
  } else {
    // 选择新的分类
    selectedCategoryId.value = category.id
    selectedCategory.value = category
  }
}

/** 清空选择 */
const handleClear = () => {
  selectedParentId.value = undefined
  selectedParentCategory.value = undefined
  selectedCategoryId.value = undefined
  selectedCategory.value = undefined
}

/** 确认选择 */
const handleConfirm = () => {
  emit('update:modelValue', selectedCategoryId.value)
  emit('change', selectedCategory.value)
  dialogVisible.value = false
}

/** 获取分类的完整显示名称（父类/子类格式） */
const getCategoryFullDisplayName = (categoryId: number | undefined): string => {
  if (!categoryId) return ''

  // 在所有分类中查找目标分类
  for (const parent of categoryTree.value) {
    if (parent.children) {
      const child = parent.children.find((c) => c.id === categoryId)
      if (child) {
        return `${parent.nameZh}/${child.nameZh}`
      }
    }
  }

  return `ID:${categoryId}`
}

/** 关闭弹窗时重置状态 */
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时不重置选择状态，保持用户的选择
  }
})

// 暴露方法给父组件
defineExpose({
  open,
  getCategoryFullDisplayName
})
</script>

<style scoped>
.category-selector {
  min-height: 500px;
}

.category-selector .el-checkbox {
  pointer-events: none;
}

.category-selector .el-checkbox__input {
  pointer-events: auto;
}

/* 进一步紧凑化样式 */
.category-selector .el-checkbox {
  margin-right: 6px;
}

.category-selector .el-checkbox__label {
  padding-left: 6px;
}

/* 减少子分类项的间距 */
.category-selector .grid > div {
  min-height: 36px;
}
</style>
