import request from '@/config/axios'

// 代购包裹 VO
export interface ParcelVO {
  id: number // 编号
  no: string // 流水号
  userId: number // 用户编号
  status: number // 包裹状态
  productCount: number // 包裹商品数量
  cancelType: number // 取消类型
  remark: string // 平台备注
  commentStatus: boolean // 是否评价
  brokerageUserId: number // 推广人编号
  payOrderId: number // 支付订单编号
  payOrderIds: string // 支付订单编号集合
  payStatus: boolean // 是否支付
  payTime: Date // 订单支付时间
  payChannelCode: string // 支付成功的支付渠道
  finishTime: Date // 包裹完成时间
  cancelTime: Date // 包裹取消时间
  totalPrice: number // 订单原价
  discountPrice: number // 订单优惠
  deliveryPrice: number // 运费金额
  insurancePrice: number // 保险金额
  servicePrice: number // 服务金额
  platformPrice:number // 平台佣金
  adjustPrice: number // 订单调价
  payPrice: number // 应付金额
  weight: number // 商品重量
  volume: number // 商品体积
  length: number // 长
  width: number // 宽
  height: number // 高
  packingWeight: number // 包装重量
  deliveryType: number // 配送类型
  transportPlanId: number // 物流方案编号
  transportCompanyId: number // 物流公司编号
  transportPlanFeeId: number // 物流方案价格编号
  transportNo: string // 物流单号
  transportTrack: string // 物流跟踪
  declareContent: string // 海关申报内容
  declareValue: number // 海关申报价值
  clearanceCode: string // 清关代码
  insuranceServices: string // 保险服务数组
  freeServices: string // 免费服务数组
  chargeServices: string // 收费服务数组
  deliveryTime: Date // 发货时间
  receiveTime: Date // 收货时间
  receiverName: string // 收件人名称
  receiverMobile: string // 收件人手机
  receiverAreaId: number // 收件人地区编号
  receiverDetailAddress: string // 收件人详细地址
  receiverPhoneCode: string // 收件人手机
  receiverPostCode: string // 邮编
  receiverCountryCode: string // 国家编码
  refundStatus: number // 售后状态
  refundPrice: number // 退款金额
  couponId: number // 优惠劵编号
  couponPrice: number // 优惠劵减免金额
  usePoint: number // 使用的积分
  pointPrice: number // 积分抵扣的金额
  givePoint: number // 赠送的积分
  vipPrice: number // VIP 减免金额
  giveCouponTemplateCounts: string // 赠送的优惠劵
  giveCouponIds: string // 赠送的优惠劵编号
  transportCompanyName: string // 物流公司名称
  transportPlanName: string // 物流方案名称
  freeServerList: ParcelServerVO // 服务列表
  chargeServerList: ParcelServerVO // 收费服务列表
  insuranceServerList: ParcelServerVO // 保险服务列表
}

export interface ParcelItemVO {
  id: number // 编号
  parcelId: number // 包裹编号
  spuId: number // 商品 SPU 编号
  spuName: string // 商品 SPU 名称
  skuId: number // 商品 SKU 编号
  picUrl: string // 商品图片
  count: number // 购买数量

}

export interface ParcelServerVO{
  id: number //  编号
  code:  string // 服务编码
  type: number // 服务类型
  name: string // 服务名称
  description: string // 服务描述
  price: number // 服务价格
  sort: number // 排序
}

export interface ParcelTransportVO{
  id?: number //  编号
  transportCompanyId?: number // 物流公司编号
  transportCompanyName: string // 物流公司名称
  transportPlanId?: number | undefined // 物流方案编号
  transportPlanName: string // 物流方案名称
  transportPlanFeeId?: number | undefined // 物流方案价格编号
  transportNo: string // 物流单号
  transportTrack?: string // 物流跟踪
}



  // 查询代购包裹分页
  export const  getParcelPage= async (params: any) => {
    return await request.get({ url: `/agent/parcel/page`, params })
  }

  // 查询代购包裹详情
  export const getParcel= async (id: number) => {
    return await request.get({ url: `/agent/parcel/get-detail?id=` + id })
  }

  // 新增代购包裹
  export const createParcel= async (data: ParcelVO) => {
    return await request.post({ url: `/agent/parcel/create`, data })
  }

  // 修改代购包裹
  export const updateParcel= async (data: ParcelVO) => {
    return await request.put({ url: `/agent/parcel/update`, data })
  }

  // 删除代购包裹
  export const deleteParcel= async (id: number) => {
    return await request.delete({ url: `/agent/parcel/delete?id=` + id })
  }

  // 导出代购包裹 Excel
  export const exportParcel= async (params) => {
    return await request.download({ url: `/agent/parcel/export-excel`, params })
  }

  // 订单发货
export const deliveryOrder = async (data: ParcelTransportVO) => {
  return await request.put({ url: `/agent/parcel/delivery`, data })
}

// 订单备注
export const updateOrderRemark = async (data: any) => {
  return await request.put({ url: `/agent/parcel/update-remark`, data })
}

// 订单调价
export const updateOrderPrice = async (data: any) => {
  return await request.put({ url: `/agent/parcel/update-price`, data })
}

// 修改订单地址
export const updateOrderAddress = async (data: any) => {
  return await request.put({ url: `/agent/parcel/update-address`, data })
}


// ==================== 子表（代购包裹明细） ====================

  // 获得代购包裹明细列表
  export const getParcelItemListByParcelId= async (parcelId) => {
    return await request.get({ url: `/agent/parcel/parcel-item/list-by-parcel-id?parcelId=` + parcelId })
  }
