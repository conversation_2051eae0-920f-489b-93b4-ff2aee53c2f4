/* 浮动按钮样式 */
#quick-add-btn {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 999999;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#quick-add-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 25px rgba(76, 175, 80, 0.6);
    background: linear-gradient(135deg, #45a049, #4CAF50);
}

#quick-add-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 确认弹窗样式 */
#quick-add-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000000;
    display: none;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#quick-add-modal.show {
    display: flex;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    flex: 1;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #666;
}

.product-info {
    margin-bottom: 25px;
}

.product-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.4;
}

.product-url {
    font-size: 12px;
    color: #666;
    word-break: break-all;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.language-select {
    margin-bottom: 20px;
}

.language-select label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.language-select select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 80px;
}

.btn-cancel {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-cancel:hover {
    background: #e9ecef;
}

.btn-confirm {
    background: #4CAF50;
    color: white;
}

.btn-confirm:hover {
    background: #45a049;
}

.btn-confirm:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* 状态提示 */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1000001;
    animation: statusSlideIn 0.3s ease;
    max-width: 300px;
}

@keyframes statusSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.status-success {
    background: #4CAF50;
}

.status-error {
    background: #f44336;
}
