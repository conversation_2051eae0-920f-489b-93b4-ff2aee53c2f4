import request from '@/config/axios'

// 代购服务项目 VO
export interface ServeVO {
  id: number // 服务编号
  code: string // 服务编码
  icon: string // 服务图标
  type: number // 类型;保险，免费服务，收费服务
  nameZh: string // 中文名称
  nameEn: string // 英文名称
  nameFr: string // 法语名称
  nameDe: string // 德语名称
  nameEs: string // 西班牙语名称
  nameAr: string // 阿拉伯语名称
  descriptionZh: string // 中文描述
  descriptionEn: string // 英文描述
  descriptionFr: string // 法语描述
  descriptionDe: string // 德语描述
  descriptionEs: string // 西班牙语描述
  descriptionAr: string // 阿拉伯语描述
  free: boolean // 是否免费
  price: number // 收费金额，单位使用：分
  sort: number // 排序
  status: number // 开启状态
}

// 代购服务项目 API
export const ServeApi = {
  // 查询代购服务项目分页
  getServePage: async (params: any) => {
    return await request.get({ url: `/agent/serve/page`, params })
  },

  // 查询代购服务项目详情
  getServe: async (id: number) => {
    return await request.get({ url: `/agent/serve/get?id=` + id })
  },

  // 新增代购服务项目
  createServe: async (data: ServeVO) => {
    return await request.post({ url: `/agent/serve/create`, data })
  },

  // 修改代购服务项目
  updateServe: async (data: ServeVO) => {
    return await request.put({ url: `/agent/serve/update`, data })
  },

  // 删除代购服务项目
  deleteServe: async (id: number) => {
    return await request.delete({ url: `/agent/serve/delete?id=` + id })
  },

  // 导出代购服务项目 Excel
  exportServe: async (params) => {
    return await request.download({ url: `/agent/serve/export-excel`, params })
  }
}