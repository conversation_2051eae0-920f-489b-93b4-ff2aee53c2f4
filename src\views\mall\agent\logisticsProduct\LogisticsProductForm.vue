<template>
  <Dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="1200px"
    class="logistics-product-form"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      class="form-container"
    >
      <!-- 基础信息区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:info-filled" class="mr-2 text-blue-600" />
          基础信息
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="物流公司" prop="companyId">
              <el-select
                v-model="formData.companyId"
                placeholder="请选择物流公司"
                class="w-full"
                @change="handleCompanyChange"
              >
                <el-option
                  v-for="item in logisticsCompanyList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id as number"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品编码" prop="productCode">
              <el-input v-model="formData.productCode" placeholder="产品编码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="渠道编码" prop="channelCode">
              <el-input v-model="formData.channelCode" placeholder="渠道编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="中文名称" prop="nameZh">
              <el-input v-model="formData.nameZh" placeholder="中文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文名称" prop="nameEn">
              <el-input v-model="formData.nameEn" placeholder="英文名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法语名称" prop="nameFr">
              <el-input v-model="formData.nameFr" placeholder="法语名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="德语名称" prop="nameDe">
              <el-input v-model="formData.nameDe" placeholder="德语名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="西班牙语" prop="nameEs">
              <el-input v-model="formData.nameEs" placeholder="西班牙语名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阿拉伯语" prop="nameAr">
              <el-input v-model="formData.nameAr" placeholder="阿拉伯语名称" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="产品图标" prop="iconUrl">
              <UploadImg v-model="formData.iconUrl" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 配置信息区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:setting" class="mr-2 text-orange-600" />
          配置信息
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最小重量(g)" prop="minWeight">
              <el-input v-model="formData.minWeight" placeholder="最小重量" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大重量(g)" prop="maxWeight">
              <el-input v-model="formData.maxWeight" placeholder="最大重量" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="体积重基数" prop="volumeBase">
              <el-input v-model="formData.volumeBase" placeholder="体积重计算基数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="包税" prop="taxInclude">
              <el-radio-group v-model="formData.taxInclude">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计算体积重" prop="needVolumeCal">
              <el-radio-group v-model="formData.needVolumeCal">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="免费保险" prop="freeInsure">
              <el-radio-group v-model="formData.freeInsure">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="IOSS" prop="iossEnabled">
              <el-radio-group v-model="formData.iossEnabled">
                <el-radio :label="true">启用</el-radio>
                <el-radio :label="false">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio
                  v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
                  :key="dict.value"
                  :label="dict.value"
                >
                  {{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="formData.sort"
                :min="0"
                :precision="0"
                :step="1"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <!-- 申报配置区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:money" class="mr-2 text-yellow-600" />
          申报配置
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最小申报价值" prop="minDeclareValue">
              <el-input v-model="formData.minDeclareValue" placeholder="最小申报价值(分)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大申报价值" prop="maxDeclareValue">
              <el-input v-model="formData.maxDeclareValue" placeholder="最大申报价值(分)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="默认申报类型" prop="defaultDeclareType">
              <el-select
                v-model="formData.defaultDeclareType"
                placeholder="请选择默认申报类型"
                class="w-full"
              >
                <el-option label="请选择字典生成" value="" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="每公斤申报价值" prop="declarePerKg">
              <el-input v-model="formData.declarePerKg" placeholder="每公斤申报价值(分)" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申报比例" prop="declareRatio">
              <el-input v-model="formData.declareRatio" placeholder="申报比例" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计算公式表达式" prop="calculationFormula">
              <el-input v-model="formData.calculationFormula" placeholder="计算公式表达式" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="分类限制配置" prop="categoryRestrictions">
              <div class="category-restrictions-config">
                <div
                  class="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-gray-50"
                >
                  <div class="flex items-center">
                    <Icon icon="ep:setting" class="mr-2 text-blue-600" />
                    <span class="text-sm text-gray-700">
                      {{ getCategoryRestrictionsDisplay() }}
                    </span>
                  </div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="openCategoryRestrictionsDialog"
                    plain
                  >
                    <Icon icon="ep:edit" class="mr-1" /> 配置分类限制
                  </el-button>
                </div>
                <!-- 详细显示已配置的分类限制 -->
                <div v-if="categoryRestrictions.length > 0" class="mt-3">
                  <div class="text-xs text-gray-500 mb-2">已配置的分类限制：</div>
                  <div class="space-y-2">
                    <div
                      v-for="restriction in categoryRestrictions"
                      :key="restriction.id"
                      class="text-xs bg-white p-2 rounded border"
                    >
                      <div class="font-medium text-gray-700 mb-1">
                        {{ getCategoryName(restriction.id) }}
                      </div>
                      <div class="flex gap-4">
                        <div>
                          <span class="text-green-600">允许:</span>
                          <span class="text-gray-600">{{
                            restriction.allowList.length > 0
                              ? restriction.allowList.map((id) => getCategoryName(id)).join(', ')
                              : '无'
                          }}</span>
                        </div>
                        <div>
                          <span class="text-red-600">禁止:</span>
                          <span class="text-gray-600">{{
                            restriction.blockList.length > 0
                              ? restriction.blockList.map((id) => getCategoryName(id)).join(', ')
                              : '无'
                          }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 特性描述区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:document" class="mr-2 text-purple-600" />
          特性描述
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="中文特性" prop="featuresZh">
              <el-input v-model="formData.featuresZh" placeholder="中文特性描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文特性" prop="featuresEn">
              <el-input v-model="formData.featuresEn" placeholder="英文特性描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法语特性" prop="featuresFr">
              <el-input v-model="formData.featuresFr" placeholder="法语特性描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="德语特性" prop="featuresDe">
              <el-input v-model="formData.featuresDe" placeholder="德语特性描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="西班牙语" prop="featuresEs">
              <el-input v-model="formData.featuresEs" placeholder="西班牙语特性描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阿拉伯语" prop="featuresAr">
              <el-input v-model="formData.featuresAr" placeholder="阿拉伯语特性描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 尺寸限制区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:scale-to-original" class="mr-2 text-red-600" />
          尺寸限制
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="中文尺寸限制" prop="dimensionRestrictionZh">
              <el-input v-model="formData.dimensionRestrictionZh" placeholder="中文尺寸限制描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文尺寸限制" prop="dimensionRestrictionEn">
              <el-input v-model="formData.dimensionRestrictionEn" placeholder="英文尺寸限制描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法语尺寸限制" prop="dimensionRestrictionFr">
              <el-input v-model="formData.dimensionRestrictionFr" placeholder="法语尺寸限制描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="德语尺寸限制" prop="dimensionRestrictionDe">
              <el-input v-model="formData.dimensionRestrictionDe" placeholder="德语尺寸限制描述" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="西班牙尺寸限制" prop="dimensionRestrictionEs">
              <el-input
                v-model="formData.dimensionRestrictionEs"
                placeholder="西班牙尺寸限制描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阿拉伯尺寸限制" prop="dimensionRestrictionAr">
              <el-input
                v-model="formData.dimensionRestrictionAr"
                placeholder="阿拉伯尺寸限制描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 体积计费规则区域 -->
      <div class="form-section">
        <div class="section-title">
          <Icon icon="ep:box" class="mr-2 text-indigo-600" />
          体积计费规则
        </div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="中文体积规则" prop="volumeWeightRuleZh">
              <el-input v-model="formData.volumeWeightRuleZh" placeholder="中文体积重量计费规则" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文体积规则" prop="volumeWeightRuleEn">
              <el-input v-model="formData.volumeWeightRuleEn" placeholder="英文体积重量计费规则" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="法语体积规则" prop="volumeWeightRuleFr">
              <el-input v-model="formData.volumeWeightRuleFr" placeholder="法语体积重量计费规则" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="德语体积规则" prop="volumeWeightRuleDe">
              <el-input v-model="formData.volumeWeightRuleDe" placeholder="德语体积重量计费规则" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="西班牙体积规则" prop="volumeWeightRuleEs">
              <el-input
                v-model="formData.volumeWeightRuleEs"
                placeholder="西班牙体积重量计费规则"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="阿拉伯体积规则" prop="volumeWeightRuleAr">
              <el-input
                v-model="formData.volumeWeightRuleAr"
                placeholder="阿拉伯体积重量计费规则"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>

  <!-- 分类限制配置弹窗 -->
  <CategoryRestrictionsDialog
    ref="categoryRestrictionsDialogRef"
    @confirm="handleCategoryRestrictionsConfirm"
  />
</template>
<script setup lang="ts">
import { getIntDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { LogisticsProductApi, LogisticsProductVO } from '@/api/mall/agent/logisticsProduct'
import { LogisticsCompanyApi } from '@/api/mall/agent/logisticsCompany'
import { CategoryApi, type CategoryVO } from '@/api/mall/agent/category'
import CategoryRestrictionsDialog from './components/CategoryRestrictionsDialog.vue'

/** 代购物流公司产品 表单 */
defineOptions({ name: 'LogisticsProductForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  companyId: undefined,
  productCode: undefined,
  channelCode: undefined,
  nameZh: undefined,
  nameEn: undefined,
  nameFr: undefined,
  nameDe: undefined,
  nameEs: undefined,
  nameAr: undefined,
  featuresZh: undefined,
  featuresEn: undefined,
  featuresFr: undefined,
  featuresDe: undefined,
  featuresEs: undefined,
  featuresAr: undefined,
  iconUrl: undefined,
  taxInclude: false,
  needVolumeCal: false,
  volumeBase: undefined,
  minWeight: undefined,
  maxWeight: undefined,
  dimensionRestrictionZh: undefined,
  dimensionRestrictionEn: undefined,
  dimensionRestrictionFr: undefined,
  dimensionRestrictionDe: undefined,
  dimensionRestrictionEs: undefined,
  dimensionRestrictionAr: undefined,
  volumeWeightRuleZh: undefined,
  volumeWeightRuleEn: undefined,
  volumeWeightRuleFr: undefined,
  volumeWeightRuleDe: undefined,
  volumeWeightRuleEs: undefined,
  volumeWeightRuleAr: undefined,
  minDeclareValue: undefined,
  maxDeclareValue: undefined,
  defaultDeclareType: undefined,
  declarePerKg: undefined,
  declareRatio: undefined,
  freeInsure: false,
  iossEnabled: false,
  calculationFormula: undefined,
  categoryRestrictions: undefined as string | undefined,
  sort: 0,
  status: 0
})
const formRules = reactive({
  companyId: [{ required: true, message: '物流公司编号不能为空', trigger: 'blur' }],
  productCode: [{ required: true, message: '产品编码不能为空', trigger: 'blur' }],
  nameZh: [{ required: true, message: '中文名称不能为空', trigger: 'blur' }],
  nameEn: [{ required: true, message: '英文名称不能为空', trigger: 'blur' }],
  taxInclude: [{ required: true, message: '是否包税不能为空', trigger: 'blur' }],
  needVolumeCal: [{ required: true, message: '是否计算体积重不能为空', trigger: 'blur' }],
  freeInsure: [{ required: true, message: '免费保险不能为空', trigger: 'blur' }],
  iossEnabled: [{ required: true, message: 'IOSS不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LogisticsProductApi.getLogisticsProduct(id)

      // 解析分类限制配置
      if (formData.value.categoryRestrictions) {
        try {
          categoryRestrictions.value = JSON.parse(formData.value.categoryRestrictions)
        } catch (e) {
          console.warn('解析分类限制配置失败:', e)
          categoryRestrictions.value = []
        }
      } else {
        categoryRestrictions.value = []
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const logisticsCompanyList = ref<any[]>([])

// 分类数据
const categoryList = ref<CategoryVO[]>([])

// 分类限制配置相关
const categoryRestrictionsDialogRef = ref()
const categoryRestrictions = ref<CategoryRestriction[]>([])

// 分类限制类型定义
interface CategoryRestriction {
  id: number // 父分类ID
  allowList: number[] // 允许的子分类ID列表
  blockList: number[] // 不允许的子分类ID列表
}

onMounted(async () => {
  // 获得物流公司列表
  const companyData = await LogisticsCompanyApi.getLogisticsCompanySimpleList()
  logisticsCompanyList.value = companyData

  // 获得分类列表
  const categoryData = await CategoryApi.getCategoryList({})
  categoryList.value = categoryData
})

/** 处理货运公司选择变化 */
const handleCompanyChange = (companyId: number) => {
  // 根据选择的公司ID，设置公司名称
  // const selectedCompany = logisticsCompanyList.value.find((item) => item.id === companyId)
  // if (selectedCompany) {
  //   formData.value.companyName = selectedCompany.name
  // }
}

/** 分类限制配置相关方法 */
const getCategoryName = (categoryId: number): string => {
  const category = categoryList.value.find((c) => c.id === categoryId)
  return category ? category.nameZh : `ID:${categoryId}`
}

const getCategoryRestrictionsDisplay = (): string => {
  if (categoryRestrictions.value.length === 0) {
    return '未配置分类限制'
  }
  const totalAllowed = categoryRestrictions.value.reduce(
    (sum, item) => sum + item.allowList.length,
    0
  )
  const totalBlocked = categoryRestrictions.value.reduce(
    (sum, item) => sum + item.blockList.length,
    0
  )
  return `已配置 ${categoryRestrictions.value.length} 个分类，允许 ${totalAllowed} 个，禁止 ${totalBlocked} 个`
}

const openCategoryRestrictionsDialog = () => {
  categoryRestrictionsDialogRef.value?.open(categoryRestrictions.value)
}

const handleCategoryRestrictionsConfirm = (restrictions: CategoryRestriction[]) => {
  categoryRestrictions.value = restrictions
  // 将分类限制转换为JSON字符串保存到表单数据中
  formData.value.categoryRestrictions = JSON.stringify(restrictions)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LogisticsProductVO
    if (formType.value === 'create') {
      await LogisticsProductApi.createLogisticsProduct(data)
      message.success(t('common.createSuccess'))
    } else {
      await LogisticsProductApi.updateLogisticsProduct(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    companyId: undefined,
    productCode: undefined,
    channelCode: undefined,
    nameZh: undefined,
    nameEn: undefined,
    nameFr: undefined,
    nameDe: undefined,
    nameEs: undefined,
    nameAr: undefined,
    featuresZh: undefined,
    featuresEn: undefined,
    featuresFr: undefined,
    featuresDe: undefined,
    featuresEs: undefined,
    featuresAr: undefined,
    iconUrl: undefined,
    taxInclude: false,
    needVolumeCal: false,
    volumeBase: undefined,
    minWeight: undefined,
    maxWeight: undefined,
    dimensionRestrictionZh: undefined,
    dimensionRestrictionEn: undefined,
    dimensionRestrictionFr: undefined,
    dimensionRestrictionDe: undefined,
    dimensionRestrictionEs: undefined,
    dimensionRestrictionAr: undefined,
    volumeWeightRuleZh: undefined,
    volumeWeightRuleEn: undefined,
    volumeWeightRuleFr: undefined,
    volumeWeightRuleDe: undefined,
    volumeWeightRuleEs: undefined,
    volumeWeightRuleAr: undefined,
    minDeclareValue: undefined,
    maxDeclareValue: undefined,
    defaultDeclareType: undefined,
    declarePerKg: undefined,
    declareRatio: undefined,
    freeInsure: false,
    iossEnabled: false,
    calculationFormula: undefined,
    categoryRestrictions: undefined,
    sort: 0,
    status: 0
  }

  // 重置分类限制配置
  categoryRestrictions.value = []

  formRef.value?.resetFields()
}
</script>

<style scoped>
.logistics-product-form {
  .form-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
  }

  .form-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e8e8e8;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e8e8e8;
    display: flex;
    align-items: center;
    color: #333;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input__wrapper) {
    border-radius: 6px;
  }

  :deep(.el-select .el-input__wrapper) {
    border-radius: 6px;
  }
}
</style>
