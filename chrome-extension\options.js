// 配置页面脚本
document.addEventListener('DOMContentLoaded', function () {
  const form = document.getElementById('settingsForm')
  const testBtn = document.getElementById('testBtn')
  const status = document.getElementById('status')

  // 加载保存的设置
  loadSettings()

  // 保存设置
  form.addEventListener('submit', function (e) {
    e.preventDefault()
    saveSettings()
  })

  // 测试连接
  testBtn.addEventListener('click', testConnection)

  function loadSettings() {
    chrome.storage.sync.get(['apiUrl', 'token', 'tenantId', 'language'], function (result) {
      document.getElementById('apiUrl').value =
        result.apiUrl || 'http://localhost:81/admin-api/product/spu/create-product-by-url'
      document.getElementById('token').value = result.token || ''
      document.getElementById('tenantId').value = result.tenantId || ''
      document.getElementById('language').value = result.language || 'zh'
    })
  }

  function saveSettings() {
    const settings = {
      apiUrl: document.getElementById('apiUrl').value.trim(),
      token: document.getElementById('token').value.trim(),
      tenantId: document.getElementById('tenantId').value.trim(),
      language: document.getElementById('language').value
    }

    // 验证必填字段
    if (!settings.apiUrl || !settings.token || !settings.tenantId) {
      showStatus('请填写所有必填字段', 'error')
      return
    }

    // 验证URL格式
    try {
      new URL(settings.apiUrl)
    } catch (e) {
      showStatus('请输入有效的API地址', 'error')
      return
    }

    chrome.storage.sync.set(settings, function () {
      if (chrome.runtime.lastError) {
        showStatus('保存失败: ' + chrome.runtime.lastError.message, 'error')
      } else {
        showStatus('设置保存成功！', 'success')
      }
    })
  }

  async function testConnection() {
    const apiUrl = document.getElementById('apiUrl').value.trim()
    const token = document.getElementById('token').value.trim()
    const tenantId = document.getElementById('tenantId').value.trim()

    if (!apiUrl || !token || !tenantId) {
      showStatus('请先填写完整的配置信息', 'error')
      return
    }

    testBtn.disabled = true
    testBtn.textContent = '测试中...'

    try {
      // 发送测试请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          'tenant-id': tenantId
        },
        body: JSON.stringify({
          url: 'https://test.example.com/test-product',
          lang: 'zh'
        })
      })

      if (response.ok) {
        showStatus('API连接测试成功！', 'success')
      } else {
        const errorData = await response.json().catch(() => ({}))
        showStatus(`连接失败: ${errorData.message || response.statusText}`, 'error')
      }
    } catch (error) {
      showStatus(`连接失败: ${error.message}`, 'error')
    } finally {
      testBtn.disabled = false
      testBtn.textContent = '测试API连接'
    }
  }

  function showStatus(message, type) {
    status.textContent = message
    status.className = `status ${type}`
    status.style.display = 'block'

    // 3秒后自动隐藏
    setTimeout(() => {
      status.style.display = 'none'
    }, 3000)
  }
})
