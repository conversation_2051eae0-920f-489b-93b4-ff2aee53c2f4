// 后台脚本
chrome.runtime.onInstalled.addListener(() => {
  console.log('商品快速添加助手已安装')

  // 设置默认配置
  chrome.storage.sync.get(['apiUrl', 'token', 'tenantId'], (result) => {
    if (!result.apiUrl) {
      chrome.storage.sync.set({
        apiUrl: 'http://localhost:81/admin-api/product/spu/create-product-by-url',
        token: '',
        tenantId: '',
        language: 'zh'
      })
    }
  })
})

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'addProduct') {
    addProductToStore(request.data)
      .then((response) => {
        sendResponse({ success: true, data: response })
      })
      .catch((error) => {
        sendResponse({ success: false, error: error.message })
      })
    return true // 保持消息通道开放
  }
})

// 调用API添加商品
async function addProductToStore(productData) {
  try {
    console.log('开始添加商品:', productData)

    // 获取配置
    const config = await new Promise((resolve) => {
      chrome.storage.sync.get(['apiUrl', 'token', 'tenantId', 'language'], resolve)
    })

    console.log('插件配置:', {
      apiUrl: config.apiUrl,
      hasToken: !!config.token,
      hasTenantId: !!config.tenantId,
      language: config.language
    })

    if (!config.apiUrl || !config.token || !config.tenantId) {
      throw new Error('请先在插件设置中配置API地址、Token和租户ID')
    }

    const requestData = {
      url: productData.url,
      lang: config.language || 'zh'
    }

    const requestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.token}`,
        'tenant-id': config.tenantId
      },
      body: JSON.stringify(requestData)
    }

    console.log('发送API请求:', {
      url: config.apiUrl,
      data: requestData,
      headers: requestOptions.headers
    })

    const response = await fetch(config.apiUrl, requestOptions)

    console.log('API响应状态:', response.status, response.statusText)

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      try {
        const errorData = await response.json()
        console.log('API错误响应:', errorData)
        errorMessage = errorData.message || errorData.msg || errorMessage
      } catch (e) {
        console.log('无法解析错误响应:', e)
      }
      throw new Error(errorMessage)
    }

    const result = await response.json()
    console.log('API成功响应:', result)
    return result
  } catch (error) {
    console.error('添加商品失败:', error)
    throw error
  }
}
