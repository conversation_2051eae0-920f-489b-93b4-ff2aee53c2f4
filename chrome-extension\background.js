// 后台脚本
chrome.runtime.onInstalled.addListener(() => {
  console.log('商品快速添加助手已安装');
  
  // 设置默认配置
  chrome.storage.sync.get(['apiUrl', 'token', 'tenantId'], (result) => {
    if (!result.apiUrl) {
      chrome.storage.sync.set({
        apiUrl: 'http://localhost:80/admin-api/product/spu/create-product-by-url',
        token: '',
        tenantId: '',
        language: 'zh'
      });
    }
  });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'addProduct') {
    addProductToStore(request.data)
      .then(response => {
        sendResponse({ success: true, data: response });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // 保持消息通道开放
  }
});

// 调用API添加商品
async function addProductToStore(productData) {
  try {
    // 获取配置
    const config = await new Promise((resolve) => {
      chrome.storage.sync.get(['apiUrl', 'token', 'tenantId', 'language'], resolve);
    });

    if (!config.apiUrl || !config.token || !config.tenantId) {
      throw new Error('请先在插件设置中配置API地址、Token和租户ID');
    }

    const response = await fetch(config.apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.token}`,
        'tenant-id': config.tenantId
      },
      body: JSON.stringify({
        url: productData.url,
        lang: config.language || 'zh'
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('添加商品失败:', error);
    throw error;
  }
}
