<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择国家"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="country-select-dialog">
      <!-- 搜索框 -->
      <div class="mb-4">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索国家名称或编码"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <Icon icon="ep:search" />
          </template>
        </el-input>
      </div>

      <!-- 已选择的国家 -->
      <div v-if="selectedCountries.length > 0" class="mb-4">
        <div class="text-sm text-gray-600 mb-2">已选择 {{ selectedCountries.length }} 个国家：</div>
        <div class="flex flex-wrap gap-2">
          <el-tag
            v-for="country in selectedCountries"
            :key="country.code"
            closable
            @close="removeCountry(country.code)"
            class="mr-1 mb-1"
          >
            {{ country.name }} ({{ country.code }})
          </el-tag>
        </div>
      </div>

      <!-- 国家列表 -->
      <div class="country-list" style="max-height: 400px; overflow-y: auto">
        <div class="grid grid-cols-4 gap-2">
          <div
            v-for="country in filteredCountries"
            :key="country.code"
            class="country-item"
            :class="{ selected: isSelected(country.code) }"
            @click="toggleCountry(country)"
          >
            <el-checkbox
              :model-value="isSelected(country.code)"
              @change="toggleCountry(country)"
              class="mr-2"
            />
            <span class="country-name">{{ country.name }}</span>
            <span class="country-code text-gray-500 text-sm ml-1">({{ country.code }})</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredCountries.length === 0" class="text-center py-8 text-gray-500">
        <Icon icon="ep:search" class="text-4xl mb-2" />
        <div>未找到匹配的国家</div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600"> 已选择 {{ selectedCountries.length }} 个国家 </div>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirm"
            :disabled="selectedCountries.length === 0"
          >
            确定 ({{ selectedCountries.length }})
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { CountryApi } from '@/api/mall/agent/country'

interface CountryItem {
  code: string
  name: string
}

const dialogVisible = ref(false)
const searchKeyword = ref('')
const countryList = ref<CountryItem[]>([])
const filteredCountries = ref<CountryItem[]>([])
const selectedCountries = ref<CountryItem[]>([])

/** 打开弹窗 */
const open = async (preSelectedCodes: string[] = []) => {
  dialogVisible.value = true
  searchKeyword.value = ''

  // 加载国家列表
  if (countryList.value.length === 0) {
    try {
      const data = await CountryApi.getCountrySimpleList()
      countryList.value = data
    } catch (error) {
      console.error('加载国家列表失败:', error)
    }
  }

  // 设置预选择的国家
  selectedCountries.value = countryList.value.filter((country) =>
    preSelectedCodes.includes(country.code)
  )

  // 初始化过滤列表
  handleSearch()
}

/** 搜索处理 */
const handleSearch = () => {
  const keyword = searchKeyword.value.toLowerCase().trim()
  if (!keyword) {
    filteredCountries.value = countryList.value
  } else {
    filteredCountries.value = countryList.value.filter(
      (country) =>
        country.name.toLowerCase().includes(keyword) || country.code.toLowerCase().includes(keyword)
    )
  }
}

/** 检查是否已选择 */
const isSelected = (code: string) => {
  return selectedCountries.value.some((country) => country.code === code)
}

/** 切换国家选择状态 */
const toggleCountry = (country: CountryItem) => {
  const index = selectedCountries.value.findIndex((item) => item.code === country.code)
  if (index > -1) {
    selectedCountries.value.splice(index, 1)
  } else {
    selectedCountries.value.push(country)
  }
}

/** 移除国家 */
const removeCountry = (code: string) => {
  const index = selectedCountries.value.findIndex((country) => country.code === code)
  if (index > -1) {
    selectedCountries.value.splice(index, 1)
  }
}

/** 确认选择 */
const emit = defineEmits(['confirm'])
const handleConfirm = () => {
  emit('confirm', selectedCountries.value)
  dialogVisible.value = false
}

defineExpose({ open })
</script>

<style scoped>
.country-select-dialog {
  font-size: 14px;
}

.country-item {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
}

.country-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.country-item.selected {
  border-color: #409eff;
  background-color: #e1f3ff;
}

.country-name {
  font-weight: 500;
}

.country-code {
  margin-left: auto;
}
</style>
