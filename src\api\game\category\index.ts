import request from '@/config/axios'

// 游戏分类 VO
export interface CategoryVO {
  id: number // 分类编号
  name: string // 分类名称
  picUrl: string // 移动端分类图
  description: string // 分类描述
  sort: number // 分类排序
  status: number // 开启状态
}

// 游戏分类 API
export const CategoryApi = {
  // 查询游戏分类分页
  getCategoryPage: async (params: any) => {
    return await request.get({ url: `/game/category/page`, params })
  },

  // 查询游戏分类详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/game/category/get?id=` + id })
  },

  // 新增游戏分类
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/game/category/create`, data })
  },

  // 修改游戏分类
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/game/category/update`, data })
  },

  // 删除游戏分类
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/game/category/delete?id=` + id })
  },

  // 导出游戏分类 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/game/category/export-excel`, params })
  },

  // 获得分类列表
  getCategoryList: () => {
    return request.get({ url: '/game/category/list' })
  }

}

