import request from '@/config/axios'

// 帮助分类 VO
export interface HelpCategoryVO {
  id: number // 帮助分类编号
  code: string // 分类编码;用于前端路由
  icon: string // 图标
  titleZh: string // 中文标题
  titleEn: string // 英文标题
  titleFr: string // 法语标题
  titleDe: string // 德语标题
  titleEs: string // 西班牙语标题
  titleAr: string // 阿拉伯语标题
  descriptionZh: string // 中文描述
  descriptionEn: string // 英文描述
  descriptionFr: string // 法语描述
  descriptionDe: string // 德语描述
  descriptionEs: string // 西班牙语描述
  descriptionAr: string // 阿拉伯语描述
  status: number // 状态
  sort: number // 排序
}

// 帮助分类 API
export const HelpCategoryApi = {
  // 查询帮助分类分页
  getHelpCategoryPage: async (params: any) => {
    return await request.get({ url: `/promotion/help-category/page`, params })
  },

  // 查询帮助分类详情
  getHelpCategory: async (id: number) => {
    return await request.get({ url: `/promotion/help-category/get?id=` + id })
  },

  // 新增帮助分类
  createHelpCategory: async (data: HelpCategoryVO) => {
    return await request.post({ url: `/promotion/help-category/create`, data })
  },

  // 修改帮助分类
  updateHelpCategory: async (data: HelpCategoryVO) => {
    return await request.put({ url: `/promotion/help-category/update`, data })
  },

  // 删除帮助分类
  deleteHelpCategory: async (id: number) => {
    return await request.delete({ url: `/promotion/help-category/delete?id=` + id })
  },

  // 导出帮助分类 Excel
  exportHelpCategory: async (params) => {
    return await request.download({ url: `/promotion/help-category/export-excel`, params })
  },

  // 查询帮助分类列表
  getHelpCategoryList: async () => {
    return await request.get({ url: `/promotion/help-category/list` })
  },
  
}