import request from '@/config/axios'

// 代购转运单 VO
export interface TransferVO {
  id: number // 转运单编号
  no: string // 转运单流水号
  terminal: number // 转运单来源终端
  userId: number // 用户编号
  userIp: string // 用户 IP
  warehouseId: number // 仓库编号
  logisticsId: number // 发货物流公司编号
  logisticsNo: string // 物流公司单号
  logisticsRemark: string // 物流备注
  itemCount: number // 转运的商品数量
  totalPrice: number // 商品总价，单位：分
  weight: number // 商品重量，单位：kg 千克
  volume: number // 商品体积，单位：cm^3
  length: number // 长，单位：cm
  width: number // 宽，单位：cm
  height: number // 高，单位：cm
  status: number // 状态
  remark: string // 备注
}

// 代购转运单 API
export const TransferApi = {
  // 查询代购转运单分页
  getTransferPage: async (params: any) => {
    return await request.get({ url: `/agent/transfer/page`, params })
  },

  // 查询代购转运单详情
  getTransfer: async (id: number) => {
    return await request.get({ url: `/agent/transfer/get?id=` + id })
  },

  // 新增代购转运单
  createTransfer: async (data: TransferVO) => {
    return await request.post({ url: `/agent/transfer/create`, data })
  },

  // 修改代购转运单
  updateTransfer: async (data: TransferVO) => {
    return await request.put({ url: `/agent/transfer/update`, data })
  },

  // 删除代购转运单
  deleteTransfer: async (id: number) => {
    return await request.delete({ url: `/agent/transfer/delete?id=` + id })
  },

  // 导出代购转运单 Excel
  exportTransfer: async (params) => {
    return await request.download({ url: `/agent/transfer/export-excel`, params })
  },

// ==================== 子表（代购转运单明细） ====================

  // 获得代购转运单明细列表
  getTransferItemListByTransferId: async (transferId) => {
    return await request.get({ url: `/agent/transfer/transfer-item/list-by-transfer-id?transferId=` + transferId })
  }
}