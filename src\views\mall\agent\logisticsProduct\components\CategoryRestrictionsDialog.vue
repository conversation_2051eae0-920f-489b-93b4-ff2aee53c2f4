<template>
  <Dialog title="分类限制配置" v-model="dialogVisible" width="1000px">
    <div class="category-restrictions-dialog">
      <!-- 说明文字 -->
      <div class="mb-3 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
        <div class="text-xs text-blue-800">
          <Icon icon="ep:info-filled" class="mr-1" />
          配置说明：选择每个大分类中允许的子分类，未勾选的子分类将被视为不允许的分类
        </div>
      </div>

      <!-- 分类树形结构 -->
      <div class="category-tree-container" v-loading="loading">
        <div v-if="categoryTree.length === 0 && !loading" class="text-center py-8 text-gray-500">
          暂无分类数据
        </div>

        <div
          v-for="parentCategory in categoryTree"
          :key="parentCategory.id"
          class="parent-category-item"
        >
          <!-- 父分类标题 -->
          <div class="parent-category-header">
            <div class="flex items-center justify-between p-2 bg-gray-100 rounded-lg">
              <div class="flex items-center">
                <Icon icon="ep:folder" class="mr-2 text-blue-600" />
                <span class="font-medium text-gray-800">{{ parentCategory.nameZh }}</span>
                <span class="text-sm text-gray-500 ml-2">({{ parentCategory.code }})</span>
              </div>
              <div class="flex items-center space-x-2">
                <el-button
                  size="small"
                  @click="selectAllChildren(parentCategory.id, true)"
                  type="primary"
                  plain
                >
                  全选
                </el-button>
                <el-button size="small" @click="selectAllChildren(parentCategory.id, false)" plain>
                  全不选
                </el-button>
                <span class="text-sm text-gray-600">
                  已选择: {{ getSelectedChildrenCount(parentCategory.id) }} /
                  {{ parentCategory.children?.length || 0 }}
                </span>
              </div>
            </div>
          </div>

          <!-- 子分类列表 -->
          <div class="children-categories mt-2">
            <div
              v-if="!parentCategory.children || parentCategory.children.length === 0"
              class="text-center py-3 text-gray-500 text-sm"
            >
              该分类下暂无子分类
            </div>
            <div v-else class="grid grid-cols-5 gap-2">
              <div
                v-for="childCategory in parentCategory.children"
                :key="childCategory.id"
                class="child-category-item"
              >
                <el-checkbox
                  :model-value="isChildSelected(parentCategory.id, childCategory.id)"
                  @change="
                    (checked) => handleChildSelect(parentCategory.id, childCategory.id, checked)
                  "
                  class="w-full"
                >
                  <span class="text-sm text-gray-700 truncate" :title="childCategory.nameZh">
                    {{ childCategory.nameZh }}
                  </span>
                </el-checkbox>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600"> 总计：{{ getTotalSelectedCount() }} 个分类已选择 </div>
        <div>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { CategoryApi, type CategoryVO } from '@/api/mall/agent/category'

const message = useMessage()

// 弹窗控制
const dialogVisible = ref(false)
const loading = ref(false)

// 分类数据
const categoryTree = ref<CategoryTreeNode[]>([])
const selectedCategories = ref<CategoryRestriction[]>([])

// 类型定义
interface CategoryTreeNode extends CategoryVO {
  children?: CategoryTreeNode[]
}

interface CategoryRestriction {
  id: number // 父分类ID
  allowList: number[] // 允许的子分类ID列表
  blockList: number[] // 不允许的子分类ID列表
}

// 事件定义
const emit = defineEmits<{
  confirm: [restrictions: CategoryRestriction[]]
}>()

/** 打开弹窗 */
const open = async (currentRestrictions?: CategoryRestriction[]) => {
  dialogVisible.value = true
  loading.value = true

  try {
    // 加载分类数据
    await loadCategoryTree()

    // 设置当前选择的分类限制
    if (currentRestrictions) {
      selectedCategories.value = JSON.parse(JSON.stringify(currentRestrictions))
    } else {
      selectedCategories.value = []
    }
  } catch (error: any) {
    message.error('加载分类数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

/** 加载分类树数据 */
const loadCategoryTree = async () => {
  const response = await CategoryApi.getCategoryList({})
  const categories: CategoryVO[] = response

  // 构建树形结构
  const parentCategories = categories.filter((item) => item.parentId === 0)
  const childCategories = categories.filter((item) => item.parentId !== 0)

  categoryTree.value = parentCategories.map((parent) => {
    const children = childCategories.filter((child) => child.parentId === parent.id)
    return {
      ...parent,
      children: children.length > 0 ? children : undefined
    }
  })
}

/** 检查子分类是否被选中 */
const isChildSelected = (parentId: number, childId: number): boolean => {
  const restriction = selectedCategories.value.find((r) => r.id === parentId)
  return restriction ? restriction.allowList.includes(childId) : false
}

/** 处理子分类选择 */
const handleChildSelect = (parentId: number, childId: number, checked: boolean) => {
  let restriction = selectedCategories.value.find((r) => r.id === parentId)

  if (!restriction) {
    // 如果该父分类还没有限制配置，创建一个新的
    restriction = {
      id: parentId,
      allowList: [],
      blockList: []
    }
    selectedCategories.value.push(restriction)
  }

  if (checked) {
    // 添加到允许列表，从不允许列表中移除
    if (!restriction.allowList.includes(childId)) {
      restriction.allowList.push(childId)
    }
    const blockIndex = restriction.blockList.indexOf(childId)
    if (blockIndex > -1) {
      restriction.blockList.splice(blockIndex, 1)
    }
  } else {
    // 从允许列表中移除，添加到不允许列表
    const allowIndex = restriction.allowList.indexOf(childId)
    if (allowIndex > -1) {
      restriction.allowList.splice(allowIndex, 1)
    }
    if (!restriction.blockList.includes(childId)) {
      restriction.blockList.push(childId)
    }
  }
}

/** 全选/全不选子分类 */
const selectAllChildren = (parentId: number, selectAll: boolean) => {
  const parentCategory = categoryTree.value.find((p) => p.id === parentId)
  if (!parentCategory || !parentCategory.children) return

  parentCategory.children.forEach((child) => {
    handleChildSelect(parentId, child.id, selectAll)
  })
}

/** 获取已选择的子分类数量 */
const getSelectedChildrenCount = (parentId: number): number => {
  const restriction = selectedCategories.value.find((r) => r.id === parentId)
  return restriction ? restriction.allowList.length : 0
}

/** 获取总的已选择分类数量 */
const getTotalSelectedCount = (): number => {
  return selectedCategories.value.reduce((total, restriction) => {
    return total + restriction.allowList.length
  }, 0)
}

/** 确认选择 */
const handleConfirm = () => {
  // 为所有父分类生成限制配置，包括没有任何选择的分类
  const finalRestrictions: CategoryRestriction[] = []

  categoryTree.value.forEach((parentCategory) => {
    if (parentCategory.children && parentCategory.children.length > 0) {
      const allChildIds = parentCategory.children.map((child) => child.id)
      const existingRestriction = selectedCategories.value.find((r) => r.id === parentCategory.id)

      if (existingRestriction) {
        // 有选择的分类，更新blockList
        const blockList = allChildIds.filter(
          (childId) => !existingRestriction.allowList.includes(childId)
        )
        finalRestrictions.push({
          ...existingRestriction,
          blockList
        })
      } else {
        // 没有任何选择的分类，全部禁止
        finalRestrictions.push({
          id: parentCategory.id,
          allowList: [],
          blockList: allChildIds
        })
      }
    }
  })

  emit('confirm', finalRestrictions)
  dialogVisible.value = false
}

// 暴露方法
defineExpose({ open })
</script>

<style scoped>
.category-restrictions-dialog {
  max-height: 500px;
  overflow-y: auto;
}

.parent-category-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
  margin-bottom: 16px;
}

.parent-category-header {
  margin-bottom: 8px;
}

.child-category-item {
  padding: 4px 0;
  transition: all 0.2s ease;
}

.child-category-item:hover {
  background-color: rgba(64, 158, 255, 0.05);
  border-radius: 4px;
}

.child-category-item :deep(.el-checkbox) {
  width: 100%;
}

.child-category-item :deep(.el-checkbox__label) {
  width: 100%;
  padding-left: 6px;
  font-size: 13px;
  line-height: 1.3;
}

.child-category-item :deep(.el-checkbox__input) {
  margin-right: 4px;
}

.category-tree-container {
  min-height: 300px;
}
</style>
