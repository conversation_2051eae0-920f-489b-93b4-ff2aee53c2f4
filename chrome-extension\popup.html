<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品快速添加助手</title>
    <style>
        body {
            width: 320px;
            padding: 0;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .container {
            padding: 20px;
            color: white;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        .header .subtitle {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .status-item:last-child {
            margin-bottom: 0;
        }
        .status-label {
            font-size: 13px;
            opacity: 0.9;
        }
        .status-value {
            font-size: 13px;
            font-weight: 500;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-connected {
            background: #4CAF50;
        }
        .status-disconnected {
            background: #f44336;
        }
        .actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        .btn-secondary:hover {
            background: white;
        }
        .current-site {
            font-size: 12px;
            text-align: center;
            opacity: 0.8;
            margin-top: 15px;
        }
        .site-supported {
            color: #4CAF50;
        }
        .site-unsupported {
            color: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 商品快速添加助手</h1>
            <div class="subtitle">快速添加商品到您的商店</div>
        </div>
        
        <div class="status-card">
            <div class="status-item">
                <span class="status-label">配置状态</span>
                <div style="display: flex; align-items: center;">
                    <span class="status-value" id="config-status">检查中...</span>
                    <div class="status-indicator" id="config-indicator"></div>
                </div>
            </div>
            <div class="status-item">
                <span class="status-label">当前网站</span>
                <span class="status-value" id="current-site">检测中...</span>
            </div>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" id="add-current-btn" disabled>
                添加当前商品
            </button>
            <a href="options.html" target="_blank" class="btn btn-secondary">
                插件设置
            </a>
        </div>
        
        <div class="current-site" id="site-info">
            正在检测当前网站...
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
