import request from '@/config/axios'

// 游戏配置 VO
export interface ConfigVO {
  name: string // 游戏名称
  categoryId: number // 游戏分类编号
  gameType: number // 游戏类型
  introduction: string // 游戏简介
  description: string // 游戏描述
  tagIds: string // 游戏标签编号列表，以逗号分隔
  gameUrl: string // 游戏链接
  likesCount: number // like数
  dislikesCount: number // dislikes数
  picUrl: string // 游戏图片
  rating: number // 评分1-5分
  ratingCount: number // 评分次数
  reviewCount: number // 评论次数
  sort: number // 分类排序
  status: number // 开启状态
}

// 游戏配置 API
export const ConfigApi = {
  // 查询游戏配置分页
  getConfigPage: async (params: any) => {
    return await request.get({ url: `/game/config/page`, params })
  },

  // 查询游戏配置详情
  getConfig: async (id: number) => {
    return await request.get({ url: `/game/config/get?id=` + id })
  },

  // 新增游戏配置
  createConfig: async (data: ConfigVO) => {
    return await request.post({ url: `/game/config/create`, data })
  },

  // 修改游戏配置
  updateConfig: async (data: ConfigVO) => {
    return await request.put({ url: `/game/config/update`, data })
  },

  // 删除游戏配置
  deleteConfig: async (id: number) => {
    return await request.delete({ url: `/game/config/delete?id=` + id })
  },

  // 导出游戏配置 Excel
  exportConfig: async (params) => {
    return await request.download({ url: `/game/config/export-excel`, params })
  }
}