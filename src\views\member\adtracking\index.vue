<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="用户编号" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户编号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="会话ID" prop="sessionId">
        <el-input
          v-model="queryParams.sessionId"
          placeholder="请输入会话ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="广告来源" prop="utmSource">
        <el-input
          v-model="queryParams.utmSource"
          placeholder="请输入广告来源"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="广告媒介" prop="utmMedium">
        <el-input
          v-model="queryParams.utmMedium"
          placeholder="请输入广告媒介"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="广告活动" prop="utmCampaign">
        <el-input
          v-model="queryParams.utmCampaign"
          placeholder="请输入广告活动"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="关键词" prop="utmTerm">
        <el-input
          v-model="queryParams.utmTerm"
          placeholder="请输入广告关键词"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="着陆页面" prop="landingPage">
        <el-input
          v-model="queryParams.landingPage"
          placeholder="请输入着陆页面"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="来源页面" prop="referrer">
        <el-input
          v-model="queryParams.referrer"
          placeholder="请输入来源页面"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否转化" prop="isConverted">
        <el-input
          v-model="queryParams.isConverted"
          placeholder="请输入是否已转化"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="转化类型" prop="conversionType">
        <el-select
          v-model="queryParams.conversionType"
          placeholder="请选择转化类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MEMBER_AD_TRACKING_CONVERSION_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['member:ad-tracking:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['member:ad-tracking:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="用户编号" align="center" prop="userId" />
      <el-table-column label="会话ID" align="center" prop="sessionId" />
      <el-table-column label="广告来源" align="center" prop="utmSource" />
      <el-table-column label="广告媒介" align="center" prop="utmMedium" />
      <el-table-column label="广告活动" align="center" prop="utmCampaign" />
      <el-table-column label="关键词" align="center" prop="utmTerm" />
      <el-table-column label="广告内容" align="center" prop="utmContent" />
      <el-table-column label="gclid" align="center" prop="gclid" />
      <el-table-column label="fbclid" align="center" prop="fbclid" />
      <el-table-column label="着陆页面" align="center" prop="landingPage" />
      <el-table-column label="来源页面" align="center" prop="referrer" />
      <el-table-column label="用户代理" align="center" prop="userAgent" />
      <el-table-column label="IP地址" align="center" prop="ipAddress" />
      <el-table-column label="是否转化" align="center" prop="isConverted" />
      <el-table-column label="转化类型" align="center" prop="conversionType">
        <template #default="scope">
          <dict-tag
            :type="DICT_TYPE.MEMBER_AD_TRACKING_CONVERSION_TYPE"
            :value="scope.row.conversionType"
          />
        </template>
      </el-table-column>
      <el-table-column label="转化价值" align="center" prop="conversionValue" />
      <el-table-column label="转化时间" align="center" prop="conversionTime" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['member:ad-tracking:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['member:ad-tracking:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AdTrackingForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { AdTrackingApi, AdTrackingVO } from '@/api/member/adtracking'
import AdTrackingForm from './AdTrackingForm.vue'

/** 广告跟踪 列表 */
defineOptions({ name: 'MemberAdTracking' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AdTrackingVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  sessionId: undefined,
  utmSource: undefined,
  utmMedium: undefined,
  utmCampaign: undefined,
  utmTerm: undefined,
  landingPage: undefined,
  referrer: undefined,
  isConverted: undefined,
  conversionType: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AdTrackingApi.getAdTrackingPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AdTrackingApi.deleteAdTracking(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AdTrackingApi.exportAdTracking(queryParams)
    download.excel(data, '广告跟踪.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
