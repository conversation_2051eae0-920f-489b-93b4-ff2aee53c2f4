<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="模板编号" prop="templateId">
        <el-select v-model="formData.templateId" placeholder="请选择优惠劵模板">
          <el-option
            v-for="item in couponTemplateList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <!-- <el-input v-model="formData.templateId" placeholder="请输入优惠劵模板编号" /> -->
      </el-form-item>
      <el-form-item label="优惠码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入优惠码" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.PROMOTION_COUPON_CODE_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户编号" />
      </el-form-item> -->
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="formData.businessType" placeholder="请选择业务类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PROMOTION_COUPON_BUSINESS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="业务ID" prop="businessId">
        <el-input v-model="formData.businessId" placeholder="请输入业务ID" />
      </el-form-item> -->
      <el-form-item label="金额（分）" prop="discountAmount">
        <el-input v-model="formData.discountAmount" placeholder="请输入优惠金额（分）" />
      </el-form-item>
      <!-- <el-form-item label="使用时间" prop="useTime">
        <el-date-picker
          v-model="formData.useTime"
          type="date"
          value-format="x"
          placeholder="选择使用时间"
        />
      </el-form-item>
      <el-form-item label="过期时间" prop="expireTime">
        <el-date-picker
          v-model="formData.expireTime"
          type="date"
          value-format="x"
          placeholder="选择过期时间"
        />
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CouponCodeApi, CouponCodeVO } from '@/api/mall/promotion/couponcode'
import {
  CouponTemplateVO,
  getCouponTemplateSimpleList
} from '@/api/mall/promotion/coupon/couponTemplate'

/** 优惠券动态码 表单 */
defineOptions({ name: 'CouponCodeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  templateId: undefined,
  code: undefined,
  status: undefined,
  userId: undefined,
  businessType: undefined,
  businessId: undefined,
  discountAmount: undefined,
  useTime: undefined,
  expireTime: undefined
})
const formRules = reactive({
  templateId: [{ required: true, message: '优惠劵模板编号不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '优惠码不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const couponTemplateList = ref<CouponTemplateVO[]>([])

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CouponCodeApi.getCouponCode(id)
    } finally {
      formLoading.value = false
    }
  }
  if (couponTemplateList.value.length === 0) {
    const data = await getCouponTemplateSimpleList(2) //查询动态码优惠劵模板
    couponTemplateList.value = data
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CouponCodeVO
    if (formType.value === 'create') {
      await CouponCodeApi.createCouponCode(data)
      message.success(t('common.createSuccess'))
    } else {
      await CouponCodeApi.updateCouponCode(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    templateId: undefined,
    code: undefined,
    status: undefined,
    userId: undefined,
    businessType: undefined,
    businessId: undefined,
    discountAmount: undefined,
    useTime: undefined,
    expireTime: undefined
  }
  formRef.value?.resetFields()
}
</script>
