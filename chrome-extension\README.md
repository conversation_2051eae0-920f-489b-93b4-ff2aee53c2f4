# 商品快速添加助手 Chrome 插件

一个用于在购物网站上快速添加商品到您商店的Chrome浏览器插件。

## 功能特点

- 🛒 **浮动按钮**: 在支持的购物网站上显示浮动添加按钮
- ⚙️ **灵活配置**: 可配置API地址、访问令牌和租户ID
- 🌐 **多网站支持**: 支持淘宝、天猫、京东、1688、唯品会等主流购物网站
- 🌍 **多语言**: 支持中文和英文商品信息
- 📱 **响应式设计**: 美观的弹窗界面和状态提示

## 支持的购物网站

- 淘宝 (taobao.com)
- 天猫 (tmall.com)
- 京东 (jd.com)
- 1688 (1688.com)
- 唯品会 (vip.com)
- 苏宁 (suning.com)
- 当当 (dangdang.com)

## 安装方法

### 开发者模式安装

1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择插件文件夹 `chrome-extension`
5. 插件安装完成

### 配置插件

1. 点击插件图标，选择"插件设置"
2. 填写以下配置信息：
   - **API地址**: 您的后端API地址（如：`http://localhost:80/admin-api/product/spu/create-product-by-url`）
   - **访问令牌**: 用于API认证的Bearer Token
   - **租户ID**: 多租户系统中的租户标识
   - **默认语言**: 选择中文或英文
3. 点击"保存设置"
4. 可选：点击"测试API连接"验证配置是否正确

## 使用方法

### 方法一：浮动按钮
1. 在支持的购物网站上浏览商品
2. 点击页面右侧的浮动购物车按钮 🛒
3. 在弹出的确认窗口中选择语言
4. 点击"确认添加"

### 方法二：插件图标
1. 在支持的购物网站上浏览商品
2. 点击浏览器工具栏中的插件图标
3. 在弹出窗口中点击"添加当前商品"

## API接口说明

插件会向配置的API地址发送POST请求：

```javascript
// 请求头
{
  "Content-Type": "application/json",
  "Authorization": "Bearer {token}",
  "tenant-id": "{tenantId}"
}

// 请求体
{
  "url": "商品页面URL",
  "lang": "zh" // 或 "en"
}
```

## 文件结构

```
chrome-extension/
├── manifest.json          # 插件配置文件
├── background.js          # 后台脚本
├── content.js            # 内容脚本
├── content.css           # 内容脚本样式
├── popup.html            # 弹出页面
├── popup.js              # 弹出页面脚本
├── options.html          # 设置页面
├── options.js            # 设置页面脚本
├── icons/                # 图标文件夹
└── README.md             # 说明文档
```

## 开发说明

### 权限说明
- `storage`: 用于保存用户配置
- `activeTab`: 获取当前标签页信息
- `scripting`: 注入内容脚本
- `host_permissions`: 在指定网站上运行

### 自定义网站适配

要添加新的购物网站支持，请修改 `content.js` 中的 `siteConfigs` 对象：

```javascript
const siteConfigs = {
  'example.com': {
    name: '示例网站',
    titleSelector: '.product-title',
    priceSelector: '.price'
  }
};
```

## 故障排除

### 常见问题

1. **插件按钮不显示**
   - 确认当前网站在支持列表中
   - 检查浏览器控制台是否有错误信息

2. **API调用失败**
   - 检查网络连接
   - 验证API地址、Token和租户ID是否正确
   - 确认后端API服务正常运行

3. **商品信息获取不准确**
   - 不同网站的页面结构可能发生变化
   - 可能需要更新选择器配置

### 调试方法

1. 打开浏览器开发者工具 (F12)
2. 查看Console标签页的错误信息
3. 在Extensions页面查看插件的错误日志

## 更新日志

### v1.0.0
- 初始版本发布
- 支持主流购物网站
- 基础配置和API调用功能

## 许可证

本项目仅供学习和内部使用。
