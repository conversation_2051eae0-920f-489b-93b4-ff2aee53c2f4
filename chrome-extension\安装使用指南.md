# 🛒 商品快速添加助手 - 安装使用指南

## 📋 功能概述

这是一个Chrome浏览器插件，可以在购物网站上快速添加商品到您的商店系统。支持淘宝、天猫、京东、1688、唯品会等主流购物网站。

## 🚀 快速开始

### 第一步：安装插件

1. **下载插件文件**
   - 将整个 `chrome-extension` 文件夹下载到本地

2. **安装到Chrome浏览器**
   - 打开Chrome浏览器
   - 地址栏输入：`chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-extension` 文件夹
   - 安装完成！

### 第二步：配置插件

1. **打开设置页面**
   - 点击浏览器工具栏中的插件图标
   - 选择"插件设置"

2. **填写配置信息**
   ```
   API地址: http://localhost:80/admin-api/product/spu/create-product-by-url
   访问令牌: 您的Bearer Token
   租户ID: 您的租户标识
   默认语言: 中文 (zh) 或 英文 (en)
   ```

3. **测试连接**
   - 点击"测试API连接"按钮
   - 确认配置正确

### 第三步：使用插件

#### 方法一：浮动按钮（推荐）
1. 在支持的购物网站上浏览商品页面
2. 页面右侧会出现绿色的购物车浮动按钮 🛒
3. 点击按钮，确认商品信息
4. 选择语言，点击"确认添加"

#### 方法二：插件图标
1. 在商品页面点击浏览器工具栏的插件图标
2. 在弹出窗口中点击"添加当前商品"

## 🌐 支持的网站

| 网站 | 域名 | 状态 |
|------|------|------|
| 淘宝 | taobao.com | ✅ 支持 |
| 天猫 | tmall.com | ✅ 支持 |
| 京东 | jd.com | ✅ 支持 |
| 1688 | 1688.com | ✅ 支持 |
| 唯品会 | vip.com | ✅ 支持 |
| 苏宁 | suning.com | ✅ 支持 |
| 当当 | dangdang.com | ✅ 支持 |

## ⚙️ 配置说明

### API地址
- 格式：`http://域名/admin-api/product/spu/create-product-by-url`
- 示例：`http://localhost:80/admin-api/product/spu/create-product-by-url`

### 访问令牌 (Token)
- 从您的管理后台获取
- 格式：Bearer Token
- 用于API身份验证

### 租户ID
- 多租户系统中的租户标识
- 从您的系统管理员获取

## 🔧 故障排除

### 常见问题

**1. 浮动按钮不显示**
- 确认当前网站在支持列表中
- 刷新页面重试
- 检查浏览器控制台错误信息

**2. API调用失败**
- 检查网络连接
- 验证API地址是否正确
- 确认Token和租户ID有效
- 检查后端服务是否运行

**3. 商品信息获取不准确**
- 不同网站页面结构可能变化
- 尝试刷新页面
- 联系技术支持更新选择器

### 调试方法

1. **查看控制台日志**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **检查插件状态**
   - 进入 `chrome://extensions/`
   - 查看插件是否正常启用
   - 查看错误日志

## 📞 技术支持

如果遇到问题，请提供以下信息：
- Chrome浏览器版本
- 当前访问的网站URL
- 错误信息截图
- 浏览器控制台日志

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 支持7个主流购物网站
- ✅ 浮动按钮和弹窗确认
- ✅ 可配置API地址和认证信息
- ✅ 多语言支持 (中文/英文)
- ✅ 美观的用户界面

## 📝 注意事项

1. **权限说明**
   - 插件只在购物网站上运行
   - 不会收集个人隐私信息
   - 仅用于商品信息提取

2. **使用限制**
   - 需要有效的API配置
   - 需要相应的系统权限
   - 仅支持列表中的购物网站

3. **数据安全**
   - 配置信息存储在本地
   - 通过HTTPS传输数据
   - 遵循最佳安全实践

---

**祝您使用愉快！** 🎉
