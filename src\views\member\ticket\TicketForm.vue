<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="90%" :close-on-click-modal="false">
    <div class="ticket-container" v-loading="formLoading">
      <!-- 工单信息区域 -->
      <div class="ticket-header">
        <el-card class="ticket-info-card">
          <template #header>
            <div class="card-header">
              <span class="ticket-title">{{ formData.title || '工单详情' }}</span>
              <div class="ticket-status">
                <el-tag
                  :type="getStatusTagType(formData.status)"
                  size="large"
                  effect="dark"
                >
                  {{ getStatusText(formData.status) }}
                </el-tag>
                <el-tag
                  :type="getPriorityTagType(formData.priority)"
                  size="small"
                  class="ml-2"
                >
                  {{ getPriorityText(formData.priority) }}
                </el-tag>
              </div>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <label>工单号：</label>
                <span>{{ formData.no }}</span>
              </div>
              <div class="info-item">
                <label>用户编号：</label>
                <span>{{ formData.userId }}</span>
              </div>
              
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>工单类型：</label>
                <span>{{ getTypeText(formData.type) }}</span>
              </div>
              
              <!-- <div class="info-item">
                <label>管理员：</label>
                <span>{{ formData.adminId || '未分配' }}</span>
              </div> -->
              <div class="info-item" v-if="formData.rating">
                <label>用户评分：</label>
                <el-rate v-model="formData.rating" disabled show-score />
              </div>
            </el-col>

            <el-col :span="8"> 
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatTime(formData.createTime) }}</span>
              </div>
              
              <div class="info-item" >
                <label>评分备注：</label>
                <span>{{formData.ratingComment}}</span>
              </div>
            </el-col>
            
          </el-row>
          <el-row :gutter="20">
            <el-col :span="16">
              <div class="info-item">
                <label>问题描述：</label>
                <div class="description-text">{{ formData.description }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item" v-if="formData.attachmentUrls && formData.attachmentUrls.length > 0">
                <label>附件：</label>
                <div class="attachment-container">
                  <UploadImgs
                    v-model="formData.attachmentUrls"
                    :disabled="true"
                    :limit="10"
                    height="60px"
                    width="60px"
                  />
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 消息对话区域 -->
      <div class="ticket-messages">
        <el-card class="messages-card">
          <template #header>
            <div class="card-header">
              <span>工单对话</span>
              <el-button type="primary" size="small" @click="scrollToBottom">
                <Icon icon="ep:bottom" />
                滚动到底部
              </el-button>
            </div>
          </template>

          <div class="messages-container" ref="messagesContainer">
            <div
              v-for="(message, index) in ticketMessages"
              :key="message.id || index"
              class="message-item"
              :class="{ 'admin-message': message.messageType === 2, 'user-message': message.messageType === 1 }"
            >
              <div class="message-avatar">
                <el-avatar
                  :size="40"
                  :src="message.messageType === 2 ? '/admin-avatar.png' : '/user-avatar.png'"
                >
                  {{ message.messageType === 2 ? '客服' : '用户' }}
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="sender-name">{{ message.replierName || (message.messageType === 2 ? '客服' : '用户') }}</span>
                  <span class="message-time">{{ formatTime(message.createTime) }}</span>
                </div>
                <div class="message-body">
                  <div class="message-text">{{ message.content }}</div>
                  <div v-if="message.attachmentUrls && message.attachmentUrls.length > 0" class="message-attachments">
                    <UploadImgs
                      v-model="message.attachmentUrls"
                      :disabled="true"
                      :limit="10"
                      height="80px"
                      width="80px"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!ticketMessages || ticketMessages.length === 0" class="empty-messages">
              <el-empty description="暂无对话记录" />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作区域 -->
      <div class="ticket-actions">
        <el-card class="actions-card">
          <template #header>
            <span>工单操作</span>
          </template>

          <el-row :gutter="20">
            <!-- 状态操作 -->
            <el-col :span="12">
              <div class="action-section">
                <h4>状态管理</h4>
                <el-form :model="statusForm" label-width="80px">
                  <el-form-item label="当前状态">
                    <el-select v-model="statusForm.status" placeholder="请选择状态" @change="handleStatusChange">
                      <el-option
                        v-for="dict in getIntDictOptions(DICT_TYPE.TICKET_STATUS)"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分配给">
                    <el-input v-model="statusForm.adminId" placeholder="管理员编号" />
                  </el-form-item>
                </el-form>
              </div>
            </el-col>

            <!-- 快速回复 -->
            <el-col :span="12">
              <div class="action-section" v-if="formData.status !=50">
                <h4>快速回复</h4>
                <el-form :model="replyForm" label-width="80px">
                  <el-form-item label="回复模板">
                    <el-select v-model="replyForm.template" placeholder="选择模板" @change="handleTemplateChange">
                      <el-option label="问题已收到，正在处理中" value="received" />
                      <el-option label="需要更多信息" value="need_info" />
                      <el-option label="问题已解决" value="resolved" />
                      <el-option label="自定义回复" value="custom" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="回复内容">
                    <el-input
                      v-model="replyForm.content"
                      type="textarea"
                      :rows="4"
                      placeholder="请输入回复内容"
                    />
                  </el-form-item>
                  <el-form-item label="附件">
                    <UploadImgs
                      v-model="replyForm.attachmentUrls"
                      :limit="5"
                      height="60px"
                      width="60px"
                    />
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="handleReply" :loading="replyLoading">
                      发送回复
                    </el-button>
                    <el-button @click="resetReplyForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>


<script setup lang="ts">
import { getIntDictOptions,getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TicketApi, TicketVO, TicketMessageVO } from '@/api/member/ticket'
import { formatDate } from '@/utils/formatTime'
import { UploadImgs } from '@/components/UploadFile'

/** 用户工单 表单 */
defineOptions({ name: 'TicketForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 工单基本信息
const formData = ref<any>({
  id: undefined,
  no: undefined,
  title: undefined,
  userId: undefined,
  type: undefined,
  priority: undefined,
  attachmentUrls: [],
  description: undefined,
  adminId: undefined,
  rating: undefined,
  ratingComment: undefined,
  closedTime: undefined,
  status: undefined,
  createTime: undefined
})

// 工单消息列表
const ticketMessages = ref<any[]>([])
const messagesContainer = ref()

// 状态操作表单
const statusForm = ref({
  status: undefined,
  adminId: undefined
})

// 回复表单
const replyForm = ref({
  template: '',
  content: '',
  attachmentUrls: []
})
const replyLoading = ref(false)

// 工具方法
const formatTime = (time: any) => {
  if (!time) return ''
  return formatDate(time, 'YYYY-MM-DD HH:mm:ss')
}

const getStatusText = (status: number) => {
  console.log(status, 'status')
  console.log(getIntDictOptions(DICT_TYPE.TICKET_STATUS))
  const dict = getIntDictOptions(DICT_TYPE.TICKET_STATUS).find(item => item.value === status)
  return dict?.label || '未知状态'
}

const getStatusTagType = (status: number) => {
  const statusMap = {
    10: 'warning', // 待处理
    20: 'primary', // 处理中
    30: 'info',    // 待用户回复
    40: 'success', // 已解决
    50: 'info'     // 已关闭
  }
  return statusMap[status] || 'info'
}

const getPriorityText = (priority: number) => {
  const dict = getIntDictOptions(DICT_TYPE.TICKET_PRIORITY).find(item => item.value === priority)
  return dict?.label || '未知优先级'
}

const getPriorityTagType = (priority: number) => {
  const priorityMap = {
    1: 'danger',  // 高
    2: 'warning', // 中
    3: 'info'     // 低
  }
  return priorityMap[priority] || 'info'
}

const getTypeText = (type: number) => {
  const dict = getIntDictOptions(DICT_TYPE.TICKET_TYPE).find(item => item.value === type)
  return dict?.label || '未知类型'
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 状态变更处理
const handleStatusChange = async (newStatus: number) => {
  try {
    // 更新工单状态
    const updateStatusData = {
      id : formData.value.id,
      status: newStatus,
    }

    await TicketApi.updateTicketStatus(updateStatusData)

    formData.value.status = newStatus
    statusForm.value.status = newStatus

    message.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    message.error('状态更新失败')
    // 恢复原状态
    statusForm.value.status = formData.value.status
  }
}

// 模板变更处理
const handleTemplateChange = (template: string) => {
  const templates = {
    received: '您好，您的问题我们已经收到，正在为您处理中，请耐心等待。',
    need_info: '您好，为了更好地为您解决问题，我们需要您提供更多详细信息。',
    resolved: '您好，您的问题已经解决，如有其他疑问请随时联系我们。',
    custom: ''
  }
  replyForm.value.content = templates[template] || ''
}

// 发送回复
const handleReply = async () => {
  if (!replyForm.value.content.trim()) {
    message.error('请输入回复内容')
    return
  }

  replyLoading.value = true
  try {
    const replyData: TicketMessageVO = {
      ticketId: formData.value.id!,
      messageType: 2, // 管理员回复
      content: replyForm.value.content,
      attachmentUrls: replyForm.value.attachmentUrls,
      replierId: 1, // 当前管理员ID，实际应该从用户信息获取
      replierName: '客服' // 当前管理员名称，实际应该从用户信息获取
    }

    // 调用API添加回复消息
    await TicketApi.createTicketMessage(replyData)

    // 重新加载消息列表
    await loadTicketMessages(formData.value.id!)

    // 重置表单
    resetReplyForm()

    // 滚动到底部
    scrollToBottom()

    message.success('回复发送成功')
  } catch (error) {
    console.error('发送回复失败:', error)
    message.error('发送回复失败')
  } finally {
    replyLoading.value = false
  }
}

// 重置回复表单
const resetReplyForm = () => {
  replyForm.value = {
    template: '',
    content: '',
    attachmentUrls: []
  }
}

// 加载工单消息
const loadTicketMessages = async (ticketId: number) => {
  try {
    const messages = await TicketApi.getTicketMessageListByTicketId(ticketId)
    ticketMessages.value = messages || []
    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('加载工单消息失败:', error)
    ticketMessages.value = []
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'update' ? '工单处理' : '工单详情'
  formType.value = type
  resetForm()

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const ticketData = await TicketApi.getTicket(id)
      formData.value = {
        ...ticketData,
        // attachmentUrls: ticketData.attachmentUrls ? JSON.parse(ticketData.attachmentUrls) : []
        attachmentUrls: ticketData.attachmentUrls || []
      }

      // 初始化状态表单
      statusForm.value = {
        status: ticketData.status,
        adminId: ticketData.adminId
      }

      // 加载工单消息
      await loadTicketMessages(id)
    } finally {
      formLoading.value = false
    }
  }
}

defineExpose({
  open,
  ticketMessages,
  scrollToBottom,
  formData,
  statusForm,
  replyForm
}) // 提供方法和数据给外部访问

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    no: undefined,
    title: undefined,
    userId: undefined,
    type: undefined,
    priority: undefined,
    attachmentUrls: [],
    description: undefined,
    adminId: undefined,
    rating: undefined,
    ratingComment: undefined,
    closedTime: undefined,
    status: undefined,
    createTime: undefined
  }

  ticketMessages.value = []
  resetReplyForm()

  statusForm.value = {
    status: undefined,
    adminId: undefined
  }
}
</script>

<style scoped lang="scss">
.ticket-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 80vh;
  overflow: hidden;
}

.ticket-header {
  flex-shrink: 0;
}

.ticket-info-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .ticket-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }

    .ticket-status {
      display: flex;
      align-items: center;
    }
  }

  .info-item {
    margin-bottom: 12px;

    label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    .description-text {
      margin-top: 4px;
      padding: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;
      line-height: 1.5;
      max-height: 100px;
      overflow-y: auto;
    }

    .attachment-container {
      margin-top: 8px;
    }
  }
}

.ticket-messages {
  flex: 1;
  overflow: hidden;

  .messages-card {
    height: 100%;
    display: flex;
    flex-direction: column;

    :deep(.el-card__body) {
      flex: 1;
      overflow: hidden;
      padding: 0;
    }
  }

  .messages-container {
    height: 100%;
    overflow-y: auto;
    padding: 16px;

    .message-item {
      display: flex;
      margin-bottom: 20px;

      &.admin-message {
        flex-direction: row-reverse;

        .message-content {
          margin-right: 12px;
          margin-left: 0;

          .message-body {
            background-color: #409eff;
            color: white;
          }
        }
      }

      &.user-message {
        .message-content {
          margin-left: 12px;
        }
      }

      .message-avatar {
        flex-shrink: 0;
      }

      .message-content {
        max-width: 70%;

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .sender-name {
            font-weight: 500;
            color: #303133;
          }

          .message-time {
            font-size: 12px;
            color: #909399;
          }
        }

        .message-body {
          background-color: #f5f7fa;
          border-radius: 8px;
          padding: 12px;

          .message-text {
            line-height: 1.5;
            word-break: break-word;
          }

          .message-attachments {
            margin-top: 8px;
          }
        }
      }
    }

    .empty-messages {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }
}

.ticket-actions {
  flex-shrink: 0;

  .action-section {
    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>