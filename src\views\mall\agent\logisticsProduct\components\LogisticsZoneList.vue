<template>
  <div class="logistics-zone-list">
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="国家编码" prop="countryCode">
        <el-input
          v-model="queryParams.countryCode"
          placeholder="请输入国家编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="分区编码" prop="zoneCode">
        <el-input
          v-model="queryParams.zoneCode"
          placeholder="请输入分区编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="分区名称" prop="zoneName">
        <el-input
          v-model="queryParams.zoneName"
          placeholder="请输入分区名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['agent:logistics-zone:create']"
          :disabled="!props.productId"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="downloadTemplate"
          v-hasPermi="['agent:logistics-zone:import']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 下载模板
        </el-button>
        &nbsp;&nbsp;&nbsp;&nbsp;
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept=".xls,.xlsx"
          :disabled="!props.productId || importLoading"
        >
          <el-button
            type="warning"
            plain
            :disabled="!props.productId || importLoading"
            :loading="importLoading"
            v-hasPermi="['agent:logistics-zone:import']"
          >
            <Icon icon="ep:upload" class="mr-5px" /> {{ importLoading ? '导入中...' : '导入分区' }}
          </el-button>
        </el-upload>
        <el-checkbox v-model="updateSupport" size="small" :disabled="!props.productId" class="ml-2">
          支持更新
        </el-checkbox>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['agent:logistics-zone:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 导入结果显示 -->
    <div v-if="importResult" class="mb-4">
      <el-alert
        :type="importResult.failureCount > 0 ? 'warning' : 'success'"
        :title="getImportResultTitle()"
        :description="getImportResultDescription()"
        show-icon
        :closable="true"
        @close="importResult = null"
      />
    </div>

    <!-- 列表 -->
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      :height="height"
    >
      <el-table-column label="分区编号" align="center" prop="id" />
      <el-table-column label="国家编码" align="center" prop="countryCode" />
      <el-table-column label="分区编码" align="center" prop="zoneCode" />
      <el-table-column label="分区名称" align="center" prop="zoneName" />
      <el-table-column label="邮编配置" align="center" prop="postalCodes" show-overflow-tooltip />
      <el-table-column label="开启状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['agent:logistics-zone:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['agent:logistics-zone:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="mt-4 flex-shrink-0" v-if="total > 0">
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        layout="prev, pager, next"
        :page-sizes="[10, 20, 50]"
      />
    </div>
    <!-- 无数据提示 -->
    <div v-if="!loading && list.length === 0" class="text-center text-gray-500 py-8">
      暂无分区数据
    </div>
  </div>

  <!-- 表单弹窗：添加/修改 -->
  <LogisticsZoneForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { LogisticsZoneApi, LogisticsZoneVO } from '@/api/mall/agent/logisticsZone'
import LogisticsZoneForm from '@/views/mall/agent/logisticsZone/LogisticsZoneForm.vue'
import { DICT_TYPE } from '@/utils/dict'

/** 物流分区列表组件 */
defineOptions({ name: 'LogisticsZoneList' })

// 定义组件属性
interface Props {
  productId?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: 400
})

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LogisticsZoneVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  countryCode: undefined,
  productId: undefined,
  zoneCode: undefined,
  zoneName: undefined,
  status: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 导入功能相关
const uploadRef = ref() // 上传组件引用
const updateSupport = ref(false) // 是否支持更新已存在数据
const importResult = ref<any>(null) // 导入结果
const importLoading = ref(false) // 导入加载状态

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 设置产品ID
    queryParams.productId = props.productId
    const data = await LogisticsZoneApi.getLogisticsZonePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, props.productId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LogisticsZoneApi.deleteLogisticsZone(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LogisticsZoneApi.exportLogisticsZone(queryParams)
    download.excel(data, '物流分区信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 导入功能相关方法 */

/** 下载导入模板 */
const downloadTemplate = async () => {
  try {
    const data = await LogisticsZoneApi.downloadImportTemplate()
    download.excel(data, '物流分区导入模板.xls')
    message.success('模板下载成功')
  } catch (error) {
    message.error('模板下载失败：' + error.message)
  }
}

/** 文件选择处理 */
const handleFileChange = async (file: any) => {
  const rawFile = file.raw

  // 文件格式校验
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ]
  if (!allowedTypes.includes(rawFile.type)) {
    message.error('只支持上传 .xlsx 或 .xls 格式的文件!')
    return
  }

  // 文件大小校验
  if (rawFile.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过10MB!')
    return
  }

  // 产品选择校验
  if (!props.productId) {
    message.error('请先选择一个物流产品')
    return
  }

  // 清空之前的导入结果
  importResult.value = null

  try {
    // 确保 productId 是数字类型
    const productId = Number(props.productId)
    if (!productId || isNaN(productId)) {
      message.error('产品ID无效，请重新选择产品')
      return
    }

    // 开始导入
    importLoading.value = true
    const result = await LogisticsZoneApi.importLogisticsZone(
      productId,
      rawFile,
      updateSupport.value
    )

    // 设置导入结果
    importResult.value = result.data

    // 显示成功消息
    const { createSuccessCount, updateSuccessCount, failureCount } = result.data
    if (failureCount === 0) {
      message.success(`导入成功！创建${createSuccessCount}条，更新${updateSuccessCount}条`)
    } else {
      message.warning(
        `导入完成！创建${createSuccessCount}条，更新${updateSuccessCount}条，失败${failureCount}条`
      )
    }

    // 刷新列表
    await getList()
  } catch (error) {
    message.error('导入失败：' + (error as any).message)
  } finally {
    importLoading.value = false
    // 清空文件选择
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }
}

/** 获取导入结果标题 */
const getImportResultTitle = () => {
  if (!importResult.value) return ''
  const { createSuccessCount, updateSuccessCount, failureCount } = importResult.value
  return `导入完成：成功创建${createSuccessCount}条，更新${updateSuccessCount}条，失败${failureCount}条`
}

/** 获取导入结果描述 */
const getImportResultDescription = () => {
  if (!importResult.value || !importResult.value.failureData) return ''
  return importResult.value.failureData
    .slice(0, 3) // 只显示前3条失败信息
    .map((item: any) => `第${item.row}行：${item.reason}`)
    .join('；')
}

// 监听产品ID变化
watch(
  () => props.productId,
  (newProductId) => {
    if (newProductId) {
      queryParams.pageNo = 1
      getList()
    } else {
      list.value = []
      total.value = 0
    }
  },
  { immediate: true }
)

// 暴露刷新方法
defineExpose({
  getList
})
</script>

<style scoped>
.logistics-zone-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.logistics-zone-list .el-table {
  flex: 1;
}
</style>
