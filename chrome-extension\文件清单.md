# Chrome插件文件清单

## 📁 文件结构

```
chrome-extension/
├── manifest.json          # 插件配置文件 (必需)
├── background.js          # 后台服务脚本
├── content.js            # 内容脚本 (注入到网页)
├── content.css           # 内容脚本样式
├── popup.html            # 插件图标点击弹窗页面
├── popup.js              # 弹窗页面脚本
├── options.html          # 插件设置页面
├── options.js            # 设置页面脚本
├── icons/                # 图标文件夹
│   └── README.md         # 图标说明
├── README.md             # 英文说明文档
├── 安装使用指南.md        # 中文使用指南
└── 文件清单.md           # 本文件
```

## 📋 文件说明

### 核心文件

1. **manifest.json** - 插件配置文件
   - 定义插件基本信息
   - 声明权限和资源
   - 配置内容脚本和后台脚本

2. **background.js** - 后台服务脚本
   - 处理API调用
   - 管理插件配置
   - 消息传递中转

3. **content.js** - 内容脚本
   - 在购物网站上注入浮动按钮
   - 提取商品信息
   - 显示确认弹窗

4. **content.css** - 样式文件
   - 浮动按钮样式
   - 弹窗样式
   - 状态提示样式

### 用户界面

5. **popup.html/js** - 插件图标弹窗
   - 显示插件状态
   - 快速添加商品
   - 跳转到设置页面

6. **options.html/js** - 设置页面
   - 配置API地址
   - 设置Token和租户ID
   - 测试API连接

### 资源文件

7. **icons/** - 图标文件夹
   - 需要添加 16x16, 48x48, 128x128 像素的图标
   - 用于插件管理页面和工具栏显示

### 文档文件

8. **README.md** - 英文技术文档
9. **安装使用指南.md** - 中文用户指南
10. **文件清单.md** - 本文件清单

## 🔧 安装前准备

### 必需文件检查
- ✅ manifest.json
- ✅ background.js
- ✅ content.js
- ✅ content.css
- ✅ popup.html
- ✅ popup.js
- ✅ options.html
- ✅ options.js

### 可选文件
- ⚠️ icons/ (建议添加图标文件)
- ✅ 文档文件

## 📦 打包说明

1. **开发模式安装**
   - 直接使用整个文件夹
   - 在Chrome扩展管理页面加载

2. **生产环境打包**
   - 可以压缩为.zip文件
   - 上传到Chrome网上应用店

## ⚠️ 注意事项

1. **图标文件**
   - 当前缺少图标文件
   - 可能导致插件显示默认图标
   - 建议添加自定义图标

2. **权限配置**
   - 已配置必要的网站权限
   - 支持主流购物网站
   - 可根据需要添加更多网站

3. **API配置**
   - 需要用户手动配置API信息
   - 首次使用需要设置Token和租户ID

## 🚀 快速开始

1. 将整个 `chrome-extension` 文件夹复制到本地
2. 打开 Chrome 浏览器
3. 进入 `chrome://extensions/`
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择 `chrome-extension` 文件夹
7. 完成安装！
