import request from '@/config/axios'

// 代购物流公司 VO
export interface LogisticsCompanyVO {
  id: number // 编号
  code: string // 物流公司编码
  name: string // 物流公司名称
  iconUrl: string // 物流公司 logo
  remark: string // 备注
  sort: number // 排序
  status: number // 状态
}

// 代购物流公司 API
export const LogisticsCompanyApi = {
  // 查询代购物流公司分页
  getLogisticsCompanyPage: async (params: any) => {
    return await request.get({ url: `/agent/logistics-company/page`, params })
  },

  getLogisticsCompanySimpleList: async () => {
    return await request.get({ url: `/agent/logistics-company/list-all-simple` })
  },

  // 查询代购物流公司详情
  getLogisticsCompany: async (id: number) => {
    return await request.get({ url: `/agent/logistics-company/get?id=` + id })
  },

  // 新增代购物流公司
  createLogisticsCompany: async (data: LogisticsCompanyVO) => {
    return await request.post({ url: `/agent/logistics-company/create`, data })
  },

  // 修改代购物流公司
  updateLogisticsCompany: async (data: LogisticsCompanyVO) => {
    return await request.put({ url: `/agent/logistics-company/update`, data })
  },

  // 删除代购物流公司
  deleteLogisticsCompany: async (id: number) => {
    return await request.delete({ url: `/agent/logistics-company/delete?id=` + id })
  },

  // 导出代购物流公司 Excel
  exportLogisticsCompany: async (params) => {
    return await request.download({ url: `/agent/logistics-company/export-excel`, params })
  },
}
