import request from '@/config/axios'

// 代购中心配置 VO
export interface ConfigVO {
  id: number // 自增主键
  afterSaleRefundReasons: string // 售后退款理由
  afterSaleReturnReasons: string // 售后退货理由
  deliveryExpressFreeEnabled: boolean // 是否启用全场包邮
  deliveryExpressFreePrice: number // 全场包邮的最小金额，单位：分
  brokerageEnabled: boolean // 是否启用分佣
  brokerageEnabledCondition: number // 分佣模式：1-人人分销 2-指定分销
  brokerageBindMode: number // 分销关系绑定模式: 1-没有推广人，2-新用户, 3-扫码覆盖
  brokeragePosterUrls: string // 分销海报图地址数组
  brokerageFirstPercent: number // 一级返佣比例
  brokerageSecondPercent: number // 二级返佣比例
  brokerageWithdrawMinPrice: number // 用户提现最低金额
  brokerageWithdrawFeePercent: number // 提现手续费百分比
  brokerageFrozenDays: number // 佣金冻结时间(天)
  brokerageWithdrawTypes: string // 提现方式：1-钱包；2-银行卡；3-微信；4-支付宝
  platformFeeType: number // 平台佣金类型
  platformFeeValue: number // 平台佣金值
  freeStorageDays: number //  免费保管天数
}
// 查询代购中心配置详情
export const getTradeConfig = async () => {
  return await request.get({ url: `/agent/config/get` })
}

// 保存代购中心配置
export const saveTradeConfig = async (data: ConfigVO) => {
  return await request.put({ url: `/agent/config/save`, data })
}

