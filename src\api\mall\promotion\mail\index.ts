import request from '@/config/axios'

// 促销邮件 VO
export interface PromotionMailVO {
  id: number // 编号
  title: string // 展示位标题
  mailTemplateId: number // 邮件模板编号
  mailTemplateCode: string // 模板编码
  banner: string // 图片
  mailTemplateParams: string // 邮件参数
  type: number // 类别;1商品，2优惠券
  status: number // 活动状态
  contentIds: string[] // 内容编号集合;商品或优惠券编号
  userType: number // 用户类别;0所有用户，1指定用户，2下单 过的用户，
  userIds: string[] // 用户编号集合
  sendTime: Date // 发送时间
  pushCount: number // 已推送次数
  memo: string // 备注
}

// 促销邮件 API
export const PromotionMailApi = {
  // 查询促销邮件分页
  getPromotionMailPage: async (params: any) => {
    return await request.get({ url: `/promotion/mail/page`, params })
  },

  // 查询促销邮件详情
  getPromotionMail: async (id: number) => {
    return await request.get({ url: `/promotion/mail/get?id=` + id })
  },

  // 新增促销邮件
  createPromotionMail: async (data: PromotionMailVO) => {
    return await request.post({ url: `/promotion/mail/create`, data })
  },

  // 修改促销邮件
  updatePromotionMail: async (data: PromotionMailVO) => {
    return await request.put({ url: `/promotion/mail/update`, data })
  },

  // 删除促销邮件
  deletePromotionMail: async (id: number) => {
    return await request.delete({ url: `/promotion/mail/delete?id=` + id })
  },

  // 导出促销邮件 Excel
  exportPromotionMail: async (params) => {
    return await request.download({ url: `/promotion/mail/export-excel`, params })
  },

  // 推送促销邮件 Excel
  pushMail: async (id: number) => {
    return await request.put({ url: `/promotion/mail/pushMail`,data:{id}})
  },

  // 测试促销邮件 Excel
  testPushMail: async (id: number,mail:string) => {
    return await request.put({ url: `/promotion/mail/previewPushMail`,data:{id,mail}})
  }
}