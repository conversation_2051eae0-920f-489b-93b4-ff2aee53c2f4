import request from '@/config/axios'

// 优惠码使用记录 VO
export interface CouponCodeUsageLogVO {
  id: number // 优惠码编号
  templateId: number // 优惠码模板编号
  couponCode: string // 使用的优惠码
  userId: number // 用户编号
  businessType: number // 业务类型
  businessId: number // 业务ID
  discountAmount: number // 优惠金额（分）
  useTime: Date // 使用时间
}

// 优惠码使用记录 API
export const CouponCodeUsageLogApi = {
  // 查询优惠码使用记录分页
  getCouponCodeUsageLogPage: async (params: any) => {
    return await request.get({ url: `/promotion/coupon-code-usage-log/page`, params })
  },

  // 查询优惠码使用记录详情
  getCouponCodeUsageLog: async (id: number) => {
    return await request.get({ url: `/promotion/coupon-code-usage-log/get?id=` + id })
  },

  // 新增优惠码使用记录
  createCouponCodeUsageLog: async (data: CouponCodeUsageLogVO) => {
    return await request.post({ url: `/promotion/coupon-code-usage-log/create`, data })
  },

  // 修改优惠码使用记录
  updateCouponCodeUsageLog: async (data: CouponCodeUsageLogVO) => {
    return await request.put({ url: `/promotion/coupon-code-usage-log/update`, data })
  },

  // 删除优惠码使用记录
  deleteCouponCodeUsageLog: async (id: number) => {
    return await request.delete({ url: `/promotion/coupon-code-usage-log/delete?id=` + id })
  },

  // 导出优惠码使用记录 Excel
  exportCouponCodeUsageLog: async (params) => {
    return await request.download({ url: `/promotion/coupon-code-usage-log/export-excel`, params })
  }
}