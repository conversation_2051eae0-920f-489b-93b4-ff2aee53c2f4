<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户编号" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户编号" />
      </el-form-item>
      <el-form-item label="会话ID" prop="sessionId">
        <el-input v-model="formData.sessionId" placeholder="请输入会话ID" />
      </el-form-item>
      <el-form-item label="广告来源" prop="utmSource">
        <el-input v-model="formData.utmSource" placeholder="请输入广告来源" />
      </el-form-item>
      <el-form-item label="广告媒介" prop="utmMedium">
        <el-input v-model="formData.utmMedium" placeholder="请输入广告媒介" />
      </el-form-item>
      <el-form-item label="广告活动" prop="utmCampaign">
        <el-input v-model="formData.utmCampaign" placeholder="请输入广告活动" />
      </el-form-item>
      <el-form-item label="广告关键词" prop="utmTerm">
        <el-input v-model="formData.utmTerm" placeholder="请输入广告关键词" />
      </el-form-item>
      <el-form-item label="广告内容" prop="utmContent">
        <Editor v-model="formData.utmContent" height="150px" />
      </el-form-item>
      <el-form-item label="Google点击ID" prop="gclid">
        <el-input v-model="formData.gclid" placeholder="请输入Google点击ID" />
      </el-form-item>
      <el-form-item label="Facebook点击ID" prop="fbclid">
        <el-input v-model="formData.fbclid" placeholder="请输入Facebook点击ID" />
      </el-form-item>
      <el-form-item label="着陆页面" prop="landingPage">
        <el-input v-model="formData.landingPage" placeholder="请输入着陆页面" />
      </el-form-item>
      <el-form-item label="来源页面" prop="referrer">
        <el-input v-model="formData.referrer" placeholder="请输入来源页面" />
      </el-form-item>
      <el-form-item label="用户代理" prop="userAgent">
        <el-input v-model="formData.userAgent" placeholder="请输入用户代理" />
      </el-form-item>
      <el-form-item label="IP地址" prop="ipAddress">
        <el-input v-model="formData.ipAddress" placeholder="请输入IP地址" />
      </el-form-item>
      <el-form-item label="是否已转化" prop="isConverted">
        <el-input v-model="formData.isConverted" placeholder="请输入是否已转化" />
      </el-form-item>
      <el-form-item label="转化类型" prop="conversionType">
        <el-select v-model="formData.conversionType" placeholder="请选择转化类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MEMBER_AD_TRACKING_CONVERSION_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转化价值" prop="conversionValue">
        <el-input v-model="formData.conversionValue" placeholder="请输入转化价值" />
      </el-form-item>
      <el-form-item label="转化时间" prop="conversionTime">
        <el-date-picker
          v-model="formData.conversionTime"
          type="date"
          value-format="x"
          placeholder="选择转化时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { AdTrackingApi, AdTrackingVO } from '@/api/member/adtracking'

/** 广告跟踪 表单 */
defineOptions({ name: 'AdTrackingForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  userId: undefined,
  sessionId: undefined,
  utmSource: undefined,
  utmMedium: undefined,
  utmCampaign: undefined,
  utmTerm: undefined,
  utmContent: undefined,
  gclid: undefined,
  fbclid: undefined,
  landingPage: undefined,
  referrer: undefined,
  userAgent: undefined,
  ipAddress: undefined,
  isConverted: undefined,
  conversionType: undefined,
  conversionValue: undefined,
  conversionTime: undefined
})
const formRules = reactive({
  sessionId: [{ required: true, message: '会话ID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AdTrackingApi.getAdTracking(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AdTrackingVO
    if (formType.value === 'create') {
      await AdTrackingApi.createAdTracking(data)
      message.success(t('common.createSuccess'))
    } else {
      await AdTrackingApi.updateAdTracking(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    userId: undefined,
    sessionId: undefined,
    utmSource: undefined,
    utmMedium: undefined,
    utmCampaign: undefined,
    utmTerm: undefined,
    utmContent: undefined,
    gclid: undefined,
    fbclid: undefined,
    landingPage: undefined,
    referrer: undefined,
    userAgent: undefined,
    ipAddress: undefined,
    isConverted: undefined,
    conversionType: undefined,
    conversionValue: undefined,
    conversionTime: undefined
  }
  formRef.value?.resetFields()
}
</script>