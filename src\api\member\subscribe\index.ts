import request from '@/config/axios'

// 用户订阅 VO
export interface SubscribeVO {
  id: number // 编号
  name: string // 用户名
  email: string // 用户邮箱
  status: number // 状态
}

// 用户订阅 API
export const SubscribeApi = {
  // 查询用户订阅分页
  getSubscribePage: async (params: any) => {
    return await request.get({ url: `/member/subscribe/page`, params })
  },

  // 查询用户订阅详情
  getSubscribe: async (id: number) => {
    return await request.get({ url: `/member/subscribe/get?id=` + id })
  },

  // 新增用户订阅
  createSubscribe: async (data: SubscribeVO) => {
    return await request.post({ url: `/member/subscribe/create`, data })
  },

  // 修改用户订阅
  updateSubscribe: async (data: SubscribeVO) => {
    return await request.put({ url: `/member/subscribe/update`, data })
  },

  // 删除用户订阅
  deleteSubscribe: async (id: number) => {
    return await request.delete({ url: `/member/subscribe/delete?id=` + id })
  },

  // 导出用户订阅 Excel
  exportSubscribe: async (params) => {
    return await request.download({ url: `/member/subscribe/export-excel`, params })
  }
}