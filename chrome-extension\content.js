// 内容脚本 - 在购物网站上注入浮动按钮
(function() {
    'use strict';
    
    let floatingBtn = null;
    let modal = null;
    let isProcessing = false;
    
    // 网站配置
    const siteConfigs = {
        'taobao.com': {
            name: '淘宝',
            titleSelector: 'h1[data-spm="1000983"], .tb-main-title, h1.tb-item-title',
            priceSelector: '.tb-rmb-num, .price-current, .tm-price-current'
        },
        'tmall.com': {
            name: '天猫',
            titleSelector: 'h1[data-spm="1000983"], .tb-main-title, h1.tb-item-title',
            priceSelector: '.tb-rmb-num, .price-current, .tm-price-current'
        },
        'jd.com': {
            name: '京东',
            titleSelector: '.sku-name, h1.product-intro-name, .p-name a',
            priceSelector: '.price, .p-price .price, .summary-price .price'
        },
        '1688.com': {
            name: '1688',
            titleSelector: '.d-title, .subject, .detail-title',
            priceSelector: '.price, .price-range, .offer-price'
        },
        'vip.com': {
            name: '唯品会',
            titleSelector: '.pib-title-detail, .goods-title',
            priceSelector: '.price-current, .goods-price'
        }
    };
    
    // 初始化
    function init() {
        if (shouldShowButton()) {
            createFloatingButton();
            createModal();
        }
    }
    
    // 判断是否应该显示按钮
    function shouldShowButton() {
        const hostname = window.location.hostname;
        return Object.keys(siteConfigs).some(domain => hostname.includes(domain));
    }
    
    // 创建浮动按钮
    function createFloatingButton() {
        if (floatingBtn) return;
        
        floatingBtn = document.createElement('button');
        floatingBtn.id = 'quick-add-btn';
        floatingBtn.innerHTML = '🛒';
        floatingBtn.title = '快速添加商品到商店';
        
        floatingBtn.addEventListener('click', showModal);
        
        document.body.appendChild(floatingBtn);
    }
    
    // 创建确认弹窗
    function createModal() {
        if (modal) return;
        
        modal = document.createElement('div');
        modal.id = 'quick-add-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🛒 添加商品到商店</h3>
                    <button class="close-btn" onclick="hideModal()">&times;</button>
                </div>
                <div class="product-info">
                    <div class="product-title" id="product-title">正在获取商品信息...</div>
                    <div class="product-url" id="product-url"></div>
                </div>
                <div class="language-select">
                    <label for="language-select">选择语言:</label>
                    <select id="language-select">
                        <option value="zh">中文</option>
                        <option value="en">英文</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button class="btn btn-cancel" onclick="hideModal()">取消</button>
                    <button class="btn btn-confirm" id="confirm-btn" onclick="confirmAdd()">确认添加</button>
                </div>
            </div>
        `;
        
        // 点击背景关闭弹窗
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                hideModal();
            }
        });
        
        document.body.appendChild(modal);
        
        // 将函数添加到全局作用域
        window.hideModal = hideModal;
        window.confirmAdd = confirmAdd;
    }
    
    // 显示弹窗
    function showModal() {
        if (!modal || isProcessing) return;
        
        // 获取商品信息
        const productInfo = getProductInfo();
        
        document.getElementById('product-title').textContent = productInfo.title;
        document.getElementById('product-url').textContent = productInfo.url;
        
        // 加载默认语言设置
        chrome.storage.sync.get(['language'], function(result) {
            const languageSelect = document.getElementById('language-select');
            if (languageSelect && result.language) {
                languageSelect.value = result.language;
            }
        });
        
        modal.classList.add('show');
    }
    
    // 隐藏弹窗
    function hideModal() {
        if (modal) {
            modal.classList.remove('show');
        }
    }
    
    // 获取商品信息
    function getProductInfo() {
        const hostname = window.location.hostname;
        const config = Object.entries(siteConfigs).find(([domain]) => hostname.includes(domain))?.[1];
        
        let title = '未知商品';
        
        if (config && config.titleSelector) {
            const titleElement = document.querySelector(config.titleSelector);
            if (titleElement) {
                title = titleElement.textContent.trim();
            }
        }
        
        // 如果没有找到标题，尝试从页面标题获取
        if (title === '未知商品') {
            title = document.title.split('-')[0].split('_')[0].trim();
        }
        
        return {
            title: title,
            url: window.location.href,
            site: config ? config.name : '未知网站'
        };
    }
    
    // 确认添加商品
    async function confirmAdd() {
        if (isProcessing) return;
        
        isProcessing = true;
        const confirmBtn = document.getElementById('confirm-btn');
        const originalText = confirmBtn.textContent;
        
        confirmBtn.textContent = '添加中...';
        confirmBtn.disabled = true;
        modal.querySelector('.modal-content').classList.add('loading');
        
        try {
            const productInfo = getProductInfo();
            const language = document.getElementById('language-select').value;
            
            // 发送消息给background script
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'addProduct',
                    data: {
                        url: productInfo.url,
                        language: language,
                        title: productInfo.title,
                        site: productInfo.site
                    }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            if (response.success) {
                showStatus('商品添加成功！', 'success');
                hideModal();
            } else {
                showStatus('添加失败: ' + response.error, 'error');
            }
        } catch (error) {
            console.error('添加商品失败:', error);
            showStatus('添加失败: ' + error.message, 'error');
        } finally {
            isProcessing = false;
            confirmBtn.textContent = originalText;
            confirmBtn.disabled = false;
            modal.querySelector('.modal-content').classList.remove('loading');
        }
    }
    
    // 显示状态消息
    function showStatus(message, type) {
        const statusDiv = document.createElement('div');
        statusDiv.className = `status-message status-${type}`;
        statusDiv.textContent = message;
        
        document.body.appendChild(statusDiv);
        
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.parentNode.removeChild(statusDiv);
            }
        }, 3000);
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 监听页面变化（SPA应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(init, 1000); // 延迟初始化，等待页面内容加载
        }
    }).observe(document, { subtree: true, childList: true });
    
})();
