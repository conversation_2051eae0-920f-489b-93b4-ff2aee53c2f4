import request from '@/config/axios'

// 用户咨询 VO
export interface InquiryVO {
  id: number // 编号
  name: string // 用户名
  email: string // 邮箱
  phone: string // 电话
  companyName: string // 公司名称
  type: string // 咨询类型
  description: string // 描述
  remark: string // 备注
  status: number // 状态
}

// 用户咨询 API
export const InquiryApi = {
  // 查询用户咨询分页
  getInquiryPage: async (params: any) => {
    return await request.get({ url: `/member/inquiry/page`, params })
  },

  // 查询用户咨询详情
  getInquiry: async (id: number) => {
    return await request.get({ url: `/member/inquiry/get?id=` + id })
  },

  // 新增用户咨询
  createInquiry: async (data: InquiryVO) => {
    return await request.post({ url: `/member/inquiry/create`, data })
  },

  // 修改用户咨询
  updateInquiry: async (data: InquiryVO) => {
    return await request.put({ url: `/member/inquiry/update`, data })
  },

  // 删除用户咨询
  deleteInquiry: async (id: number) => {
    return await request.delete({ url: `/member/inquiry/delete?id=` + id })
  },

  // 导出用户咨询 Excel
  exportInquiry: async (params) => {
    return await request.download({ url: `/member/inquiry/export-excel`, params })
  }
}