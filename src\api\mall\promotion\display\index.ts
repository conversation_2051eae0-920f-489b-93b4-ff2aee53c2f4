import request from '@/config/axios'

// 促销展示位 VO
export interface DisplayVO {
  id: number // 编号
  title: string // 展示位标题
  subTitle: string // 展示位副标题
  picUrl: string // 图片 URL
  url: string // 跳转地址
  tag: string // 标签;标签，可用来区分具体位置
  spuIds: string[] // 商品 SPU 编号集合
  skuIds: string[] // 商品 SKU 编号集合
  status: number // 活动状态
  sort: number // 排序
  position: number // 位置
  description: string // 描述
  memo: string // 备注
  browseCount: number // 展示位点击次数
  titleEn: string // 展示位标题英语
  subTitleEn: string // 展示位副标题英语
  descriptionEn: string // 描述
  titleDe: string // 展示位标题德语
  subTitleDe: string // 展示位副标题德语
  descriptionDe: string // 描述
  titleFr: string // 展示位标题法语
  subTitleFr: string // 展示位副标题法语
  descriptionFr: string // 描述
  titleEs: string // 展示位标题西语
  subTitleEs: string // 展示位副标题西语
  descriptionEs: string // 描述
  titleAr: string // 展示位标题阿语
  subTitleAr: string // 展示位副标题阿语
  descriptionAr: string // 描述
}

// 促销展示位 API
export const DisplayApi = {
  // 查询促销展示位分页
  getDisplayPage: async (params: any) => {
    return await request.get({ url: `/promotion/display/page`, params })
  },

  // 查询促销展示位详情
  getDisplay: async (id: number) => {
    return await request.get({ url: `/promotion/display/get?id=` + id })
  },

  // 新增促销展示位
  createDisplay: async (data: DisplayVO) => {
    return await request.post({ url: `/promotion/display/create`, data })
  },

  // 修改促销展示位
  updateDisplay: async (data: DisplayVO) => {
    return await request.put({ url: `/promotion/display/update`, data })
  },

  // 删除促销展示位
  deleteDisplay: async (id: number) => {
    return await request.delete({ url: `/promotion/display/delete?id=` + id })
  },

  // 导出促销展示位 Excel
  exportDisplay: async (params) => {
    return await request.download({ url: `/promotion/display/export-excel`, params })
  }
}