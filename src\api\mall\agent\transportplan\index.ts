import request from '@/config/axios'

// 国际货运方案价格 VO
export interface TransportPlanFeeVO {
  id?: number // 编号
  planId?: number // 方案编号
  basePrice?: number // 基础运费，单位：分
  attachPrice?: number // 附加费用，单位：分
  countryCode?: string // 国家编码
  countryName?: string // 国家名称
  attachServices?: string // 附加服务
  firstWeight?: number // 首重 单位：g 克
  firstWeightPrice?: number // 首重价格，单位：分
  additionalWeight?: number // 续重单位，单位：g 克
  additionalWeightPrice?: number // 续重价格，单位：分
  minWeight?: number // 最低计费重量，单位：g 克
  maxWeight?: number // 最高重量，单位：g 克
  volumeWeightRatio?: number // 体积重量的计算比例，单位：cm³/kg
  excludeAreas?: string // 排除地区
  excludePostalCodes?: string // 排除邮编
  sort?: number // 排序
  description?: string // 描述
  effectiveTime?: number // 生效时间
  status?: number // 状态
  remark?: string // 备注
}

// 国际货运方案 VO
export interface TransportPlanVO {
  id?: number // 编号
  name?: string // 方案名称
  code?: string // 方案编码
  channelCode?: string // 渠道编码
  planType?: string // 方案类型(极速，标准，经济)
  companyId?: number // 运输公司编号
  companyName?: string // 运输公司名称
  transportMethod?: string // 运输方式（空运，陆运，海运等）
  keywords?: string // 关键词
  recommendCategories?: string // 推荐品类
  battery?: boolean | string // 是否可以带电
  basePrice?: number // 基础运费，单位：分
  attachPrice?: number // 附加费用，单位：分
  countries?: string // 支持的国家
  attachServices?: string // 附加服务
  firstWeight?: number // 首重 单位：g 克
  firstWeightPrice?: number // 首重价格，单位：分
  additionalWeight?: number // 续重单位，单位：g 克
  additionalWeightPrice?: number // 续重价格，单位：分
  minWeight?: number // 最低计费重量，单位：g 克
  maxWeight?: number // 最高重量，单位：g 克
  volumetricWeightRatio?: number // 体积重量的计算比例，单位：cm³/kg
  description?: string // 方案描述
  sort?: number // 排序
  effectiveTime?: Date | number // 生效时间
  status?: number // 状态
  remark?: string // 备注
  transportPlanFees?: TransportPlanFeeVO[] // 国际货运方案价格列表
}

// 国际货运方案 API
export const TransportPlanApi = {
  // 查询国际货运方案分页
  getTransportPlanPage: async (params: any) => {
    return await request.get({ url: `/agent/transport-plan/page`, params })
  },

  // 查询国际货运方案详情
  getTransportPlan: async (id: number) => {
    return await request.get({ url: `/agent/transport-plan/get?id=` + id })
  },

  // 新增国际货运方案
  createTransportPlan: async (data: TransportPlanVO) => {
    return await request.post({ url: `/agent/transport-plan/create`, data })
  },

  // 修改国际货运方案
  updateTransportPlan: async (data: TransportPlanVO) => {
    return await request.put({ url: `/agent/transport-plan/update`, data })
  },

  // 删除国际货运方案
  deleteTransportPlan: async (id: number) => {
    return await request.delete({ url: `/agent/transport-plan/delete?id=` + id })
  },

  // 导出国际货运方案 Excel
  exportTransportPlan: async (params) => {
    return await request.download({ url: `/agent/transport-plan/export-excel`, params })
  },

  // 下载国际货运方案导入模板
  downloadTemplate: async () => {
    return await request.download({ url: `/agent/transport-plan/get-import-template` })
  },

// ==================== 子表（国际货运方案价格） ====================

  // 获得国际货运方案价格列表
  getTransportPlanFeeListByPlanId: async (planId) => {
    return await request.get({ url: `/agent/transport-plan/transport-plan-fee/list-by-plan-id?planId=` + planId })
  }
}