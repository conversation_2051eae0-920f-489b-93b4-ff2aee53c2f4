import request from '@/config/axios'

// 帮助文章 VO
export interface HelpArticleVO {
  id: number // 帮助文章编号
  categoryId: number // 分类编号
  code: string // 文章编码;用于前端路由
  author: string // 文章作者
  browseCount: number // 浏览次数
  titleZh: string // 中文标题
  titleEn: string // 英文标题
  titleFr: string // 法语标题
  titleDe: string // 德语标题
  titleEs: string // 西班牙语标题
  titleAr: string // 阿拉伯语标题
  contentZh: string // 中文内容
  contentEn: string // 英文内容
  contentFr: string // 法语内容
  contentDe: string // 德语内容
  contentEs: string // 西班牙语内容
  contentAr: string // 阿拉伯语内容
  relatedArticles: number // 相关文章ID;用逗号分隔，如"1,2,3"
  helpfulCount: number // 有帮助反馈数量
  unhelpfulCount: number // 无帮助反馈数量
  sort: number // 排序
  status: number // 状态
  faq: boolean // 是否FAQ;0-否，1-是
}

// 帮助文章 API
export const HelpArticleApi = {
  // 查询帮助文章分页
  getHelpArticlePage: async (params: any) => {
    return await request.get({ url: `/promotion/help-article/page`, params })
  },

  // 查询帮助文章详情
  getHelpArticle: async (id: number) => {
    return await request.get({ url: `/promotion/help-article/get?id=` + id })
  },

  // 新增帮助文章
  createHelpArticle: async (data: HelpArticleVO) => {
    return await request.post({ url: `/promotion/help-article/create`, data })
  },

  // 修改帮助文章
  updateHelpArticle: async (data: HelpArticleVO) => {
    return await request.put({ url: `/promotion/help-article/update`, data })
  },

  // 删除帮助文章
  deleteHelpArticle: async (id: number) => {
    return await request.delete({ url: `/promotion/help-article/delete?id=` + id })
  },

  // 导出帮助文章 Excel
  exportHelpArticle: async (params) => {
    return await request.download({ url: `/promotion/help-article/export-excel`, params })
  }
}