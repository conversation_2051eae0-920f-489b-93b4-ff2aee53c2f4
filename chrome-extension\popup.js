// Popup页面脚本
document.addEventListener('DOMContentLoaded', function() {
    const configStatus = document.getElementById('config-status');
    const configIndicator = document.getElementById('config-indicator');
    const currentSite = document.getElementById('current-site');
    const addCurrentBtn = document.getElementById('add-current-btn');
    const siteInfo = document.getElementById('site-info');
    
    // 支持的网站列表
    const supportedSites = {
        'taobao.com': '淘宝',
        'tmall.com': '天猫',
        'jd.com': '京东',
        '1688.com': '1688',
        'vip.com': '唯品会',
        'suning.com': '苏宁',
        'dangdang.com': '当当'
    };
    
    // 初始化
    init();
    
    async function init() {
        await checkConfiguration();
        await checkCurrentSite();
    }
    
    // 检查配置状态
    async function checkConfiguration() {
        try {
            const config = await new Promise((resolve) => {
                chrome.storage.sync.get(['apiUrl', 'token', 'tenantId'], resolve);
            });
            
            const isConfigured = config.apiUrl && config.token && config.tenantId;
            
            if (isConfigured) {
                configStatus.textContent = '已配置';
                configIndicator.className = 'status-indicator status-connected';
            } else {
                configStatus.textContent = '未配置';
                configIndicator.className = 'status-indicator status-disconnected';
            }
            
            return isConfigured;
        } catch (error) {
            configStatus.textContent = '检查失败';
            configIndicator.className = 'status-indicator status-disconnected';
            return false;
        }
    }
    
    // 检查当前网站
    async function checkCurrentSite() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab || !tab.url) {
                currentSite.textContent = '无法检测';
                siteInfo.textContent = '请在购物网站上使用此插件';
                return;
            }
            
            const url = new URL(tab.url);
            const hostname = url.hostname;
            
            // 检查是否为支持的网站
            const supportedSite = Object.entries(supportedSites).find(([domain]) => 
                hostname.includes(domain)
            );
            
            if (supportedSite) {
                const [domain, siteName] = supportedSite;
                currentSite.textContent = siteName;
                siteInfo.innerHTML = `<span class="site-supported">✓ 支持的购物网站</span>`;
                
                // 检查配置是否完整
                const isConfigured = await checkConfiguration();
                if (isConfigured) {
                    addCurrentBtn.disabled = false;
                    addCurrentBtn.textContent = `添加${siteName}商品`;
                } else {
                    addCurrentBtn.textContent = '请先完成配置';
                }
            } else {
                currentSite.textContent = '不支持';
                siteInfo.innerHTML = `<span class="site-unsupported">⚠ 当前网站暂不支持</span>`;
                addCurrentBtn.textContent = '网站不支持';
            }
        } catch (error) {
            console.error('检查网站失败:', error);
            currentSite.textContent = '检查失败';
            siteInfo.textContent = '无法检测当前网站';
        }
    }
    
    // 添加当前商品
    addCurrentBtn.addEventListener('click', async function() {
        if (addCurrentBtn.disabled) return;
        
        const originalText = addCurrentBtn.textContent;
        addCurrentBtn.textContent = '添加中...';
        addCurrentBtn.disabled = true;
        
        try {
            // 获取当前标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab) {
                throw new Error('无法获取当前标签页');
            }
            
            // 向content script发送消息，触发添加商品
            await chrome.tabs.sendMessage(tab.id, { action: 'showModal' });
            
            // 关闭popup
            window.close();
        } catch (error) {
            console.error('添加商品失败:', error);
            alert('添加失败: ' + error.message);
        } finally {
            addCurrentBtn.textContent = originalText;
            addCurrentBtn.disabled = false;
        }
    });
});
