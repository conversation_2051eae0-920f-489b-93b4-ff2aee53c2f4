<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>商品快速添加助手 - 设置</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
        margin-bottom: 30px;
        text-align: center;
      }
      .form-group {
        margin-bottom: 20px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #555;
      }
      input[type='text'],
      input[type='url'],
      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
      }
      input:focus,
      select:focus {
        outline: none;
        border-color: #4caf50;
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
      }
      .btn {
        background-color: #4caf50;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        width: 100%;
        margin-top: 10px;
      }
      .btn:hover {
        background-color: #45a049;
      }
      .btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
      .status {
        padding: 10px;
        border-radius: 4px;
        margin-top: 15px;
        text-align: center;
        display: none;
      }
      .status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .help-text {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }
      .test-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🛒 商品快速添加助手设置</h1>

      <form id="settingsForm">
        <div class="form-group">
          <label for="apiUrl">API地址:</label>
          <input
            type="url"
            id="apiUrl"
            placeholder="http://localhost:81/admin-api/product/spu/create-product-by-url"
            required
          />
          <div class="help-text">后端API的完整地址</div>
        </div>

        <div class="form-group">
          <label for="token">访问令牌 (Token):</label>
          <input type="text" id="token" placeholder="请输入访问令牌" required />
          <div class="help-text">用于API认证的Bearer Token</div>
        </div>

        <div class="form-group">
          <label for="tenantId">租户ID:</label>
          <input type="text" id="tenantId" placeholder="请输入租户ID" required />
          <div class="help-text">多租户系统中的租户标识</div>
        </div>

        <div class="form-group">
          <label for="language">默认语言:</label>
          <select id="language">
            <option value="zh">中文</option>
            <option value="en">英文</option>
          </select>
          <div class="help-text">商品信息的默认语言</div>
        </div>

        <button type="submit" class="btn">保存设置</button>
      </form>

      <div class="test-section">
        <h3>连接测试</h3>
        <button type="button" id="testBtn" class="btn">测试API连接</button>
      </div>

      <div id="status" class="status"></div>
    </div>

    <script src="options.js"></script>
  </body>
</html>
